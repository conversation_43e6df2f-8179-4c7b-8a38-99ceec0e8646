import React from "react";
import "./Input.css";
import { InputProps } from "../../Interfaces/input.interface";

const Input: React.FC<InputProps> = ({
  label,
  type = "text",
  name,
  value,
  onChange,
  placeholder = "",
  error = "",
  maxLength,
  required = false,
  readOnly = false,
  className = "",
  ...rest
}) => {
  return (
    <div className="input-wrapper">
      {label && <label htmlFor={name}>{label}</label>}
      <input
        id={name}
        name={name}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        maxLength={maxLength}
        required={required}
        readOnly={readOnly}
        className={`${className} ${error ? "input-error" : ""}`.trim()}
        {...rest}
      />
      {error && <span className="error-text">{error}</span>}
    </div>
  );
};

export default Input;
