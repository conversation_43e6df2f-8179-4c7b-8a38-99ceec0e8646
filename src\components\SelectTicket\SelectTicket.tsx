import React, { useEffect, useRef } from "react";
import Input from "../Input/Input";
import "./SelectTicket.css";
import {
  ITicketsByDate,
  ITicketType,
} from "../../Interfaces/event-ticket.interface";
import moment from "moment";
import { addToCart } from "../../services/user.service";
import { IVenueCartItem } from "../../Interfaces/cart.interface";

interface SelectTicketProps {
  tickets: ITicketsByDate;
  quantities: { [key: string]: number };
  setQuantities: React.Dispatch<
    React.SetStateAction<{ [key: string]: number }>
  >;
  isFreeTickets: boolean;
}

const SelectTicket: React.FC<SelectTicketProps> = ({
  tickets,
  quantities,
  setQuantities,
  isFreeTickets,
}) => {
  const allTickets: ITicketType[] = Object.values(tickets).flat();
  const hasInteractedRef = useRef(false);

  useEffect(() => {
    if (!hasInteractedRef.current) return;

    const handler = setTimeout(() => {
      const venueWiseTickets: IVenueCartItem = {};

      allTickets.forEach((ticket) => {
        const quantity = quantities[ticket._id] || 0;
        if (quantity > 0) {
          const venueId = ticket.venueId!;
          if (!venueWiseTickets[venueId]) {
            venueWiseTickets[venueId] = [];
          }

          venueWiseTickets[venueId].push({
            [ticket._id]: quantity,
          });
        }
      });

      if (!isFreeTickets) {
        addToCart(venueWiseTickets).then((res) => {
          console.log("Tickets added to cart successfully:", res);
        });
      }
    }, 500);

    return () => clearTimeout(handler);
  }, [quantities, isFreeTickets, allTickets]);

  const updateQuantity = (ticket: ITicketType, delta: number) => {
    hasInteractedRef.current = true;
    setQuantities((prev) => {
      const current = prev[ticket._id] || 0;
      const newQty = Math.max(0, Math.min(current + delta, ticket.quantity));
      return { ...prev, [ticket._id]: newQty };
    });
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    ticket: ITicketType
  ) => {
    hasInteractedRef.current = true;
    const value = +e.target.value || 0;
    setQuantities((prev) => ({
      ...prev,
      [ticket.type]: Math.min(Math.max(0, value), ticket.quantity),
    }));
  };

  if (allTickets.length === 0) {
    return (
      <div className="step-container">
        <h2>Select Ticket</h2>
        <p>No tickets available for this date and venue.</p>
      </div>
    );
  }

  return (
    <div className="step-container">
      {Object.entries(tickets).map(([date, ticketList]) => (
        <div key={date} className="ticket-group">
          <h3 className="ticket-date-heading">
            {moment(date).format("ddd,  D MMM YYYY")}
          </h3>

          <div className="ticket-card-list">
            {ticketList.map((ticket) => {
              const qty = quantities[ticket._id] || "";

              return (
                <div key={ticket.type} className="ticket-card">
                  <div className="ticket-info">
                    <div className="ticket-name">{ticket.type}</div>
                    {!isFreeTickets && (
                      <div className="ticket-price">₹ {ticket.price}</div>
                    )}
                  </div>

                  <div className="ticket-controls">
                    <div
                      className="ticket-counter"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div
                        className="counter-btn"
                        onClick={() => updateQuantity(ticket, -1)}
                      >
                        −
                      </div>
                      <Input
                        name={ticket.type}
                        placeholder="0"
                        className="counter-input"
                        type="number"
                        value={qty}
                        onChange={(e) => handleChange(e, ticket)}
                      />
                      <div
                        className="counter-btn"
                        onClick={() => updateQuantity(ticket, 1)}
                      >
                        +
                      </div>
                    </div>
                    <div className="ticket-available">
                      {ticket.quantity} Remaining
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
};

export default SelectTicket;
