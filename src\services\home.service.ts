import axios from "axios";
import { API_URL } from "../utils/env";
import {
  ICarousalData,
  IEventPosters,
} from "../Interfaces/organization.interface";

export const getHomeCarousalData = async (): Promise<ICarousalData[]> => {
  try {
    const res = await axios.get(`${API_URL}/organization/get-event-banners`);
    return res.data;
  } catch (error: any) {
    console.error("Error fetching carousal data:", error);
    throw new Error(
      error?.response?.data?.message || "Failed to fetch carousal data"
    );
  }
};

export const getHomeEventPoster = async (): Promise<IEventPosters[][]> => {
  try {
    const res = await axios.get(`${API_URL}/organization/get-event-posters`);
    return res.data;
  } catch (error: any) {
    console.error("Error fetching carousal data:", error);
    throw new Error(
      error?.response?.data?.message || "Failed to fetch carousal data"
    );
  }
};
