/* Base Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px clamp(20px, 5vw, 100px);
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  height: clamp(60px, 8vh, 88px);
  width: 100%;
  box-sizing: border-box;
}

.logo-link {
  text-decoration: none;
  color: inherit;
}

.logo {
  display: flex;
  align-items: center;
  width: clamp(120px, 20vw, 180px);
  height: clamp(40px, 8vh, 70px);
  flex-shrink: 0;
}

.header img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* User Info */
.user-badge {
  background-color: var(--secondary-color);
  color: white;
  font-size: 12px;
  font-weight: 700;
  padding: 0 10px;
  display: flex;
  align-items: center;
  height: 100%;
}

.user-greeting {
  font-size: clamp(12px, 2vw, 14px);
  font-weight: 600;
  color: var(--secondary-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: clamp(50px, 20vw, 160px);
}

/* Login Button */
.header-login-btn {
  background-color: #e63a4a;
  font-size: 16px;
  color: #fff;
  font-weight: 500;
  border: 1px solid transparent;
  border-radius: 12px;
  padding: 8px 16px;
  cursor: pointer;
  text-decoration: none;
  height: 44px;
}

/* Menu Container */
.menu-container {
  display: flex;
  align-items: center;
  gap: 5px;
  border-radius: 10px;
  border: 1px solid #e63a4a1a;
  overflow: hidden;
  position: relative;
  height: 34px;
}

.menu-icon {
  padding-right: 5px;
}

.menu-icon.open svg {
  transform: rotate(180deg) translateY(-15%);
  transition: transform 0.2s ease-in-out;
}

/* Dropdown */
.dropdown-menu {
  position: fixed;
  top: clamp(65px, calc(8vh + 5px), 95px);
  right: clamp(20px, 5vw, 100px);
  background: #fff;
  border: 1px solid #e63a4a1a;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  width: clamp(180px, 25vw, 220px);
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out;
  overflow: hidden;
  padding: 4px 0;
  max-height: 80vh;
  overflow-y: auto;
}

.dropdown-menu a,
.dropdown-menu button {
  width: 100%;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-light);
  text-decoration: none;
  cursor: pointer;
  transition: background 0.2s ease;
}

.dropdown-menu a:hover,
.dropdown-menu button:hover {
  color: var(--primary-color);
}

.divider {
  height: 1px;
  width: 90%;
  background-color: #f7f5f5;
  margin: 0 auto;
}

/* Fade Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0px);
  }
}

/* Large Desktop (≥1440px) */
@media (min-width: 1440px) {
  .header {
    padding: 20px clamp(60px, 8vw, 73px);
    height: 65px;
  }

  .logo {
    width: 160px;
    height: 60px;
  }

  .user-greeting {
    font-size: 15px;
    max-width: 180px;
  }

  .dropdown-menu {
    top: 60px;
    right: clamp(60px, 5vw, 120px);
    width: 240px;
  }
}

/* Desktop (1200px - 1439px) */
@media (max-width: 1439px) and (min-width: 1200px) {
  .header {
    padding: 18px clamp(40px, 6vw, 80px);
    height: 65px;
  }

  .logo {
    width: 140px;
    height: 50px;
  }

  .user-greeting {
    font-size: 14px;
    max-width: 160px;
  }

  .dropdown-menu {
    top: 60px;
    right: clamp(40px, 6vw, 80px);
  }
}

/* Laptop (1024px - 1199px) */
@media (max-width: 1199px) and (min-width: 1024px) {
  .header {
    padding: 16px clamp(30px, 5vw, 50px);
    height: 60px;
  }

  .logo {
    width: 140px;
    height: 50px;
  }

  .user-greeting {
    max-width: 140px;
    font-size: 14px;
  }

  .dropdown-menu {
    right: clamp(30px, 4vw, 60px);
    top: 53px;
    width: 200px;
  }
}

/* Tablet Landscape (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .header {
    padding: 14px clamp(25px, 4vw, 40px);
    height: 60px;
  }

  .logo {
    width: 140px;
    height: 50px;
  }

  .user-greeting {
    max-width: 120px;
    font-size: 13px;
  }

  .header-login-btn {
    padding: 8px 14px;
    font-size: 14px;
    height: 42px;
  }

  .dropdown-menu {
    width: 190px;
    top: 60px;
    right: clamp(25px, 4vw, 40px);
  }
}

/* Tablet Portrait (600px - 767px) */
@media (max-width: 767px) and (min-width: 600px) {
  .header {
    padding: 12px clamp(20px, 4vw, 30px);
    height: 60px;
  }

  .logo {
    width: 135px;
    height: 40px;
  }

  .user-greeting {
    max-width: 100px;
    font-size: 13px;
  }

  .header-login-btn {
    padding: 8px 12px;
    font-size: 14px;
    height: 40px;
  }

  .menu-container {
    height: 40px;
  }

  .dropdown-menu {
    width: 180px;
    top: 62px;
    right: clamp(20px, 4vw, 30px);
  }
}

/* Mobile Large (480px - 599px) */
@media (max-width: 599px) and (min-width: 480px) {
  .header {
    padding: 10px clamp(15px, 3vw, 20px);
    height: 60px;
  }

  .logo {
    width: 130px;
    height: 40px;
  }

  .user-badge {
    padding: 12px 8px;
    font-size: 11px;
  }

  .user-greeting {
    max-width: 80px;
    font-size: 12px;
  }

  .header-login-btn {
    padding: 7px 10px;
    font-size: 13px;
    height: 36px;
    border-radius: 10px;
  }

  .menu-container {
    height: 36px;
  }

  .dropdown-menu {
    width: 170px;
    top: 57px;
    right: clamp(15px, 3vw, 20px);
  }

  .dropdown-menu a,
  .dropdown-menu button {
    padding: 6px 12px;
    font-size: 13px;
  }
}

/* Mobile Medium (375px - 479px) */
@media (max-width: 479px) and (min-width: 375px) {
  .header {
    padding: 8px clamp(12px, 3vw, 16px);
    height: 50px;
  }

  .logo {
    width: 120px;
    height: 40px;
  }

  .user-badge {
    padding: 10px 6px;
    font-size: 10px;
  }

  .user-greeting {
    max-width: 70px;
    font-size: 11px;
  }

  .header-login-btn {
    padding: 6px 8px;
    font-size: 12px;
    height: 34px;
    border-radius: 8px;
  }

  .menu-container {
    height: 34px;
    gap: 3px;
  }

  .menu-icon {
    padding: 3px;
  }

  .dropdown-menu {
    width: 160px;
    top: 53px;
    right: clamp(12px, 3vw, 16px);
  }

  .dropdown-menu a,
  .dropdown-menu button {
    padding: 5px 10px;
    font-size: 12px;
    gap: 8px;
  }
}

/* Mobile Small (≤374px) */
@media (max-width: 374px) {
  .header {
    padding: 6px clamp(8px, 2vw, 12px);
    height: 50px;
  }

  .logo {
    width: 105px;
    height: 35px;
  }

  .user-badge {
    padding: 8px 5px;
    font-size: 9px;
  }

  .user-greeting {
    max-width: 60px;
    font-size: 10px;
  }

  .header-login-btn {
    padding: 5px 7px;
    font-size: 11px;
    height: 30px;
    border-radius: 6px;
  }

  .menu-container {
    height: 30px;
    gap: 2px;
    border-radius: 6px;
  }

  .menu-icon {
    padding: 2px;
  }

  .dropdown-menu {
    width: 150px;
    top: 48px;
    right: clamp(8px, 2vw, 12px);
    border-radius: 8px;
  }

  .dropdown-menu a,
  .dropdown-menu button {
    padding: 4px 8px;
    font-size: 11px;
    gap: 6px;
  }
}
