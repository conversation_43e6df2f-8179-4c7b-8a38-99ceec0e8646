import { IEvent } from "./organization.interface";

export interface ITicketType {
  _id: string;
  type: string;
  price: number;
  quantity: number;
  venueId?: string;
  count?: number;
}

export interface IEventTicket {
  id: string;
  event: string | IEvent;
  generalPrice: number;
  generalQuantity: number;
  isDifferentPrice: boolean;
  isMultiPlace: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface IVenueTicket {
  _id: string;
  venue: string;
  address: string;
  date: string;
  ticketTypes: ITicketType[];
  createdAt?: string;
  updatedAt?: string;
}
export interface IEventWithVenueTickets {
  eventTicket: IEventTicket;
  venueTickets: IVenueTicket[];
}

export interface ITicketsByDate {
  [date: string]: ITicketType[];
}
