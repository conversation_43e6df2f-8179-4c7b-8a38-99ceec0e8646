import { useEffect, useState } from "react";
import Card from "../../components/Card/Card";
import "./Home.css";
import {
  getHomeCarousalData,
  getHomeEventPoster,
} from "../../services/home.service";
import {
  ICarousalData,
  IEventPosters,
} from "../../Interfaces/organization.interface";
import Carousel from "../../components/Carousel/Carousel";
import Header from "../../components/Header/Header";
import HomeFooter from "../../components/HomeFooter/HomeFooter";

const Home = () => {
  const [carousal, setCarousal] = useState<ICarousalData[]>([]);
  const [eventPosters, setEventPosters] = useState<IEventPosters[]>([]);
  const [liveEventPosters, setLiveEventPosters] = useState<IEventPosters[]>([]);

  useEffect(() => {
    getHomeCarousalData()
      .then((data) => setCarousal(data))
      .catch((err) => console.error("Error loading data", err));

    getHomeEventPoster()
      .then((data) => {
        setLiveEventPosters(data[0]);
        setEventPosters(data[1]);
      })
      .catch((err) => console.error("Error loading data", err));
  }, []);

  return (
    <>
      <Header />
      <div className="home-container">
        <main className="main-content">
          {carousal.length > 0 && <Carousel items={carousal} />}
          <div className="card-container">
            <div className="menu-section">
              <div className="menu-item">Live Events</div>
            </div>
            <div className="card-grid">
              {liveEventPosters
                .sort(() => Math.random() - 0.5)
                .map((place, index) => (
                  <div className="card-wrapper" key={index}>
                    <Card
                      imageUrl={place.posterImage}
                      title={place.name}
                      slug={place.slug}
                      description={place.description || ""}
                    />
                  </div>
                ))}
            </div>
          </div>
          <div className="card-container card-container-even">
            <div className="menu-section">
              <div className="menu-item">Test Events</div>
            </div>
            <div className="card-grid">
              {eventPosters
                .sort(() => Math.random() - 0.5)
                .map((place, index) => (
                  <div className="card-wrapper" key={index}>
                    <Card
                      imageUrl={place.posterImage}
                      title={place.name}
                      slug={place.slug}
                      description={place.description || ""}
                    />
                  </div>
                ))}
            </div>
          </div>
        </main>
        <HomeFooter />
      </div>
    </>
  );
};

export default Home;
