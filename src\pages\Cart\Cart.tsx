import "./Cart.css";
import Input from "../../components/Input/Input";
import Header from "../../components/Header/Header";
import React, { useEffect, useRef, useState } from "react";
import {
  ICartEventTicket,
  IVenueCartItem,
} from "../../Interfaces/cart.interface";
import {
  addToCart,
  getCart,
  saveTransaction,
} from "../../services/user.service";
import moment from "moment";
import axiosInstance from "../../services/axiosInstance";
import { RAZORPAY_KEY_ID } from "../../utils/env";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "../../store/hooks";
import Loader from "../../components/Loader/Loader";

declare global {
  interface Window {
    Razorpay: any;
  }
}

const CartPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [loaderLoading, setLoaderLoading] = useState(true);
  const [selectedTickets, setSelectedTickets] = useState<ICartEventTicket[]>(
    []
  );

  const navigate = useNavigate();
  const hasInteractedRef = useRef(false);
  const { user } = useAppSelector((state) => state.user);

  useEffect(() => {
    getCart()
      .then((data) => setSelectedTickets(data))
      .catch((error) => console.error("Error fetching cart data:", error))
      .finally(() => setLoaderLoading(false));
  }, []);

  useEffect(() => {
    if (!hasInteractedRef.current) return;
    const handler = setTimeout(() => {
      const venueWiseTickets: IVenueCartItem = {};
      selectedTickets.forEach((venue) => {
        venue.ticketTypes.forEach((ticket) => {
          const count = ticket.count || 0;
          if (count >= 0) {
            const venueId = venue._id!;
            if (!venueWiseTickets[venueId]) {
              venueWiseTickets[venueId] = [];
            }
            venueWiseTickets[venueId].push({ [ticket._id]: count });
          }
        });
      });

      addToCart(venueWiseTickets)
        .then((res) => console.log("Updated cart:", res))
        .catch((err) => console.error("Cart update failed:", err));
    }, 1000);

    return () => clearTimeout(handler);
  }, [selectedTickets]);

  const updateQuantity = (ticket: any, delta: number) => {
    hasInteractedRef.current = true;
    setSelectedTickets((prev) =>
      prev.map((event) => ({
        ...event,
        ticketTypes: event.ticketTypes.map((t) =>
          t._id === ticket._id
            ? { ...t, count: Math.max((t.count || 0) + delta, 0) }
            : t
        ),
      }))
    );
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    ticket: any
  ) => {
    hasInteractedRef.current = true;
    const value = Math.max(parseInt(e.target.value) || 0, 0);
    setSelectedTickets((prev) =>
      prev.map((event) => ({
        ...event,
        ticketTypes: event.ticketTypes.map((t) =>
          t._id === ticket._id ? { ...t, count: value } : t
        ),
      }))
    );
  };

  const totalTickets = selectedTickets.reduce(
    (sum, event) =>
      sum +
      event.ticketTypes.reduce((c, ticket) => c + (ticket?.count || 0), 0),
    0
  );

  const calculateTotal = () => {
    return selectedTickets.reduce(
      (grandTotal, event) =>
        grandTotal +
        event.ticketTypes.reduce(
          (eventTotal, ticket) =>
            eventTotal + (ticket?.count || 0) * ticket.price,
          0
        ),
      0
    );
  };

  const groupedTickets = selectedTickets.reduce((acc, item) => {
    const filteredTicketTypes = item.ticketTypes.filter(
      (ticket) => (ticket.count || 0) > 0
    );
    if (filteredTicketTypes.length > 0) {
      const newItem = { ...item, ticketTypes: filteredTicketTypes };
      if (!acc[item.eventName]) acc[item.eventName] = [];
      acc[item.eventName].push(newItem);
    }
    return acc;
  }, {} as Record<string, ICartEventTicket[]>);

  const loadRazorpay = (src: any) =>
    new Promise((resolve) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = () => resolve(true);
      script.onerror = () => resolve(false);
      document.body.appendChild(script);
    });

  const handlePayment = async () => {
    try {
      if (totalTickets === 0) {
        alert("Your cart is empty. Please add tickets before proceeding.");
        return;
      }
      setLoading(true);
      const res = await loadRazorpay(
        "https://checkout.razorpay.com/v1/checkout.js"
      );

      if (!res) {
        alert("Razorpay SDK failed to load");
        return;
      }
      const amount = calculateTotal();

      const orderRes = await axiosInstance.post("payment/get-payment-intent", {
        amount,
        selectedTickets,
      });
      const { id: order_id } = orderRes.data;

      const options = {
        key: RAZORPAY_KEY_ID,
        amount: amount * 100,
        currency: "INR",
        name: "Pravesh",
        description: "Event Ticket Purchase",
        order_id,
        handler: async function (response: any) {
          try {
            await saveTransaction(response, amount, selectedTickets);
            navigate("/assign");
          } catch (err) {
            alert("Payment succeeded but post-payment failed.");
            console.error(err);
          } finally {
            setLoading(false);
          }
        },
        prefill: {
          name: user?.firstName ? user?.firstName + " " + user?.lastName : null,
          email: user?.email || null,
          contact: user?.phone || null,
        },
        theme: {
          color: "#e63a4a",
        },
      };

      const rzp = new window.Razorpay(options);
      rzp.open();
    } catch (error) {
      setLoading(false);
      console.error("Payment error:", error);
      alert(
        "An error occurred while processing your payment. Please try again."
      );
    }
  };

  if (loaderLoading) {
    return <Loader />;
  }

  return (
    <>
      <Header />
      <div className="cart-container">
        <div className="cart-content">
          <div className="cart-left">
            {Object.keys(groupedTickets).length === 0 ? (
              <p>Your cart is empty. Add some tickets!</p>
            ) : (
              Object.entries(groupedTickets).map(([eventName, venues]) => (
                <div key={eventName} className="event-group">
                  <h2 className="event-cart-title">{eventName}</h2>
                  {venues.map((venue) => (
                    <div key={venue._id}>
                      <div className="ticket-card-list">
                        {venue.ticketTypes.map((ticket) => (
                          <div key={ticket._id} className="ticket-card">
                            <div className="ticket-info">
                              <div className="ticket-name">{ticket.type}</div>
                              <div className="ticket-detail-date">
                                Date:{" "}
                                {moment(venue.date).format("MMMM Do, YYYY")}
                              </div>
                            </div>
                            <div className="ticket-controls">
                              <div className="ticket-control-wrap">
                                <div className="ticket-price">
                                  ₹ {ticket.price}
                                </div>
                                <div
                                  className="ticket-counter"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <div
                                    className="counter-btn"
                                    onClick={() => updateQuantity(ticket, -1)}
                                  >
                                    −
                                  </div>
                                  <Input
                                    name={ticket.type}
                                    placeholder="0"
                                    className="counter-input"
                                    type="number"
                                    min={0}
                                    value={ticket.count || 0}
                                    onChange={(e) => handleChange(e, ticket)}
                                  />
                                  <div
                                    className="counter-btn"
                                    onClick={() => updateQuantity(ticket, 1)}
                                  >
                                    +
                                  </div>
                                </div>
                              </div>
                              <div className="ticket-available">
                                {ticket.quantity} Remaining
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ))
            )}
          </div>

          <div className="cart-right modern-summary">
            <h2>🧾 Order Summary</h2>
            {Object.entries(groupedTickets).map(([eventName, venues]) => {
              const eventTotal = venues.reduce(
                (sum, venue) =>
                  sum +
                  venue.ticketTypes.reduce(
                    (ticketSum, ticket) =>
                      ticketSum + (ticket.count || 0) * ticket.price,
                    0
                  ),
                0
              );

              return (
                <div key={eventName} className="event-summary-card">
                  <h3>{eventName}</h3>
                  {venues.map((venue) => (
                    <ul key={venue._id} className="ticket-list">
                      <div className="ticket-date">
                        {moment(venue.date).format("DD-MM-YYYY")}
                      </div>
                      {venue.ticketTypes.map((ticket) => (
                        <li key={ticket._id}>
                          <span className="ticket-cart-name">
                            {ticket.type}
                          </span>
                          <span className="ticket-detail">
                            {ticket.count} × ₹{ticket.price} = ₹
                            {(ticket.count || 0) * ticket.price}
                          </span>
                        </li>
                      ))}
                    </ul>
                  ))}
                  <div className="event-subtotal">Subtotal: ₹{eventTotal}</div>
                </div>
              );
            })}

            <div className="summary-footer">
              <p>
                <strong>Total Tickets:</strong> {totalTickets}
              </p>
              <p>
                <strong>Final Total:</strong> ₹{calculateTotal()}
              </p>
              <button
                className="checkout-btn modern"
                disabled={totalTickets === 0 || loading}
                onClick={handlePayment}
              >
                {loading ? "Processing..." : "Proceed to Payment"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default CartPage;
