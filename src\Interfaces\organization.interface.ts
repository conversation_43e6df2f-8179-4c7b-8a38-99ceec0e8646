export interface ICarousalData {
  _id: string;
  slug: string;
  bannerImage: string;
  name: string;
  startDate: string;
  location: string;
  duration: string;
}

export interface IEventPosters {
  _id: string;
  name: string;
  slug: string;
  posterImage: string;
  description: string;
}

export interface IArtist {
  name: string;
  profileImage: string;
  order: number;
}

export interface ISponsor {
  name: string;
  profileImage: string;
  order: number;
}

export interface IEvent {
  _id: string;
  name: string;
  slug: string;
  description: string;
  startDate: string;
  location: string;
  duration: string;
  organization: string;
  posterImage: string;
  bannerImage: string;
  mainImage: string;
  isPublished: boolean;
  isDeleted: boolean;
  artists?: IArtist[];
  sponsors?: ISponsor[];
  __v: number;
}
