import { useEffect, useRef, useState } from "react";
import "./Event.css";
import { Link, useParams } from "react-router-dom";
import { getEventData } from "../../services/organization.service";
import { IEvent } from "../../Interfaces/organization.interface";
import moment from "moment";
import { Icons } from "../../components/Icons/Icons";
import Header from "../../components/Header/Header";
import { useAppSelector } from "../../store/hooks";
import Login from "../Login/Login";
import Modal from "../../components/Model/Modal";
import EventMap from "../../components/EventMap/EventMap";
import Loader from "../../components/Loader/Loader";

const EventPage = () => {
  const [eventData, setEventData] = useState<IEvent>();
  const [showMore, setShowMore] = useState(false);
  const [isClamped, setIsClamped] = useState(false);
  const [showLogin, setShowLogin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const descRef = useRef<HTMLParagraphElement>(null);
  const { user } = useAppSelector((state) => state.user);
  const { slug } = useParams<{ slug: string }>();
  useEffect(() => {
    getEventData(slug!).then((data) => {
      setEventData(data);
      setIsLoading(false);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (descRef.current) {
      const lineHeight = parseFloat(
        getComputedStyle(descRef.current).lineHeight
      );
      const maxHeight = lineHeight * 3;
      if (descRef.current.scrollHeight > maxHeight) {
        setIsClamped(true);
      }
    }
  }, [eventData?.description]);

  if (isLoading) {
    return <Loader />;
  }
  return (
    <>
      <Header />
      <div className="event-container">
        <h1 className="event-title">{eventData?.name}</h1>

        <div className="event-ticket-container">
          <div className="left-section">
            <div className="event-image-container">
              <img src={eventData?.mainImage} />
            </div>
            <div className="event-details">
              <h2>About The Event</h2>
              <p
                ref={descRef}
                className={showMore ? "show-full" : "clamped-description"}
              >
                {eventData?.description}
              </p>
              {isClamped && (
                <span
                  className="toggle-btn"
                  onClick={() => setShowMore(!showMore)}
                >
                  {showMore ? "Show less" : "Show more"}
                </span>
              )}
            </div>

            <div className="event-details-container">
              <div className="event-icon-container">
                <Icons.Calendar />
                {moment(eventData?.startDate).format("ddd DD MMM YYYY")}
              </div>
              <div className="event-icon-container">
                <Icons.Clock />
                {moment(eventData?.startDate).format("hh:mm A")}
              </div>
              {eventData?.duration && (
                <div className="event-icon-container">
                  <Icons.SandClock />
                  {eventData?.duration}
                </div>
              )}
              <div className="event-icon-container">
                <Icons.Attendees />
                All age groups
              </div>
              {eventData?.location && (
                <div className="event-icon-container">
                  <Icons.Location />
                  {eventData?.location}
                </div>
              )}
            </div>
            {eventData?.location && (
              <div className="left-event-map-container">
                <EventMap
                  lat={22.3039}
                  lng={70.8022}
                  eventName={eventData?.location || ""}
                />
              </div>
            )}
            {eventData?.artists && eventData?.artists?.length > 0 && (
              <div className="event-details">
                <h2>Artist</h2>
                <div className="artists-profile">
                  {eventData?.artists
                    ?.sort((a, b) => a.order - b.order)
                    .map((artist, index) => (
                      <div key={index}>
                        <img src={artist.profileImage} alt={artist.name} />
                        <p>{artist.name}</p>
                      </div>
                    ))}
                </div>
              </div>
            )}
            {eventData?.sponsors && eventData?.sponsors?.length > 0 && (
              <div className="event-details">
                <h2>Sponsors</h2>
                <div className="sponsor-profile">
                  {eventData?.sponsors
                    ?.sort((a, b) => a.order - b.order)
                    .map((sponsor, index) => (
                      <div key={index}>
                        <img src={sponsor.profileImage} alt={sponsor.name} />
                        <p>{sponsor.name}</p>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>

          <div className="right-section">
            <div className="event-details-section">
              <div className="event-icon-container">
                <Icons.Calendar />
                {moment(eventData?.startDate).format("ddd DD MMM YYYY")}
              </div>
              <div className="event-icon-container">
                <Icons.Clock />
                {moment(eventData?.startDate).format("hh:mm A")}
              </div>
              {eventData?.duration && (
                <div className="event-icon-container">
                  <Icons.SandClock />
                  {eventData?.duration}
                </div>
              )}
              <div className="event-icon-container">
                <Icons.Attendees />
                All age groups
              </div>
              {eventData?.location && (
                <div className="event-icon-container">
                  <Icons.Location />
                  {eventData?.location}
                </div>
              )}
              <p className="divider"></p>
              <div className="ticket-btn-section">
                <p className="price"></p>
                {user ? (
                  <Link to={`/ticket-details/${eventData?._id}`}>
                    <button className="btn">Get Tickets</button>
                  </Link>
                ) : (
                  <button className="btn" onClick={() => setShowLogin(true)}>
                    Login
                  </button>
                )}
              </div>
            </div>
            {eventData?.location && (
              <div className="event-map-container">
                <EventMap
                  lat={22.3039}
                  lng={70.8022}
                  eventName={eventData?.location || ""}
                />
              </div>
            )}
          </div>
        </div>
      </div>

      <footer className="event-footer fade-in">
        <p>&copy; 2025 Navaratri Ticket Booking. All Rights Reserved.</p>
      </footer>
      <div className="bottom-booking-container">
        <div className="ticket-btn-section">
          <p className="price">Get You Sport</p>
          {user ? (
            <Link to={`/ticket-details/${eventData?._id}`}>
              <button className=" btn">Get Tickets</button>
            </Link>
          ) : (
            <button className="btn" onClick={() => setShowLogin(true)}>
              Login
            </button>
          )}
        </div>
      </div>

      {showLogin && (
        <Modal onClose={() => setShowLogin(false)}>
          <Login isEventPopup={true} onClose={() => setShowLogin(false)} />
        </Modal>
      )}
    </>
  );
};

export default EventPage;
