const CancellationPolicyPage = () => {
  return (
    <main className="legal-page">
      <header className="legal-header">
        <h1>Cancellation Policy – Pravesh</h1>
        <p className="date">Effective Date: July 08, 2025</p>
      </header>

      <div className="legal-content">
        <section className="legal-intro">
          <p>
            At Pravesh, we value your experience and strive to offer a smooth
            ticket booking process. However, event ticket cancellations are
            subject to the rules set by individual event organizers.
          </p>
        </section>

        <section>
          <h2>1. User-Initiated Cancellations</h2>
          <ul>
            <li>
              <strong>Standard Policy:</strong> Tickets once booked cannot be
              canceled unless the event organizer explicitly allows
              cancellation.
            </li>
            <li>
              If cancellation is permitted, the specific time window and
              conditions will be mentioned on the event listing.
            </li>
            <li>
              Cancellation requests must be submitted through your account or
              emailed to{" "}
              <a href="mailto:<EMAIL>"><EMAIL></a>{" "}
              before the mentioned deadline.
            </li>
          </ul>
        </section>

        <section>
          <h2>2. Organizer-Initiated Cancellations</h2>
          <ul>
            <li>
              If the event is canceled by the organizer, your ticket will be
              automatically canceled and moved to refund processing.
            </li>
            <li>
              You will be notified via email and SMS with further steps or
              refund details.
            </li>
          </ul>
        </section>

        <section>
          <h2>3. Rescheduled Events</h2>
          <ul>
            <li>Tickets remain valid for rescheduled events.</li>
            <li>
              If you cannot attend the new date, you may request cancellation
              (if permitted by the organizer) within 72 hours of the
              rescheduling announcement.
            </li>
          </ul>
        </section>

        <section>
          <h2>4. Non-Cancellable Events</h2>
          <ul>
            <li>
              Certain events may be marked as non-cancellable. These will be
              clearly mentioned on the event booking page.
            </li>
            <li>
              By proceeding with such bookings, you acknowledge that
              cancellations are not allowed.
            </li>
          </ul>
        </section>

        <section>
          <h2>5. How to Cancel (if applicable)</h2>
          <ul>
            <li>
              To initiate a cancellation (if allowed), follow these steps:
              <br />
              <strong>
                Log in to your account → Go to “My Tickets” → Select Event →
                Request Cancellation
              </strong>
            </li>
            <li>
              OR email your booking ID and cancellation reason to{" "}
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </li>
          </ul>
        </section>
      </div>
    </main>
  );
};

export default CancellationPolicyPage;
