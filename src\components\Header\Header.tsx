import { useEffect, useRef, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import "./Header.css";
import { logout } from "../../store/slices/user-slice";
import { Link, useNavigate } from "react-router-dom";
import { RoutePaths } from "../../utils/route.enm";
import Modal from "../Model/Modal";
import Login from "../../pages/Login/Login";
import { Icons } from "../Icons/Icons";

const Header = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);

  const { user, accessToken } = useAppSelector((state) => state.user);

  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const menuRef = useRef<HTMLDivElement>(null);
  const iconRef = useRef<HTMLDivElement>(null);

  const handleLogout = () => {
    dispatch(logout());
    setMenuOpen(false);
    navigate(RoutePaths.Home);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      if (
        menuOpen &&
        menuRef.current &&
        !menuRef.current.contains(target) &&
        iconRef.current &&
        !iconRef.current.contains(target)
      ) {
        setMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [menuOpen]);

  return (
    <>
      <div className="header">
        <Link to="/" className="logo-link">
          <div className="logo">
            <img src="/logo.png" alt="Logo" />
          </div>
        </Link>

        {accessToken && user ? (
          <div className="menu-container">
            {user.firstName && (
              <p className="user-badge">
                {user.firstName[0]?.toUpperCase() +
                  user?.lastName[0]?.toUpperCase()}
              </p>
            )}
            <p className="user-greeting">
              {user?.firstName
                ? user?.firstName[0]?.toUpperCase() +
                  user.firstName.slice(1) +
                  " " +
                  user?.lastName[0]?.toUpperCase() +
                  user?.lastName?.slice(1)
                : "Guest"}
            </p>
            <div
              className={`menu-icon ${menuOpen ? "open" : ""}`}
              ref={iconRef}
              onClick={() => setMenuOpen(!menuOpen)}
            >
              <Icons.DownChevron />
            </div>
          </div>
        ) : (
          <button
            className="header-login-btn"
            onClick={() => setShowLoginModal(true)}
          >
            Get Started
          </button>
        )}
      </div>

      {menuOpen && (
        <div className="dropdown-menu" ref={menuRef}>
          <Link to="/profile" onClick={() => setMenuOpen(false)}>
            <Icons.UserProfile />
            My Profile
          </Link>
          <div className="divider"></div>
          <Link to="/assign" onClick={() => setMenuOpen(false)}>
            <Icons.Ticket />
            My Tickets
          </Link>
          <div className="divider"></div>

          <Link to="/cart" onClick={() => setMenuOpen(false)}>
            <Icons.Cart />
            Cart
          </Link>
          <div className="divider"></div>
          <button onClick={handleLogout}>
            <Icons.LogOut />
            Logout
          </button>
        </div>
      )}

      {showLoginModal && (
        <Modal onClose={() => setShowLoginModal(false)}>
          <Login isEventPopup={true} onClose={() => setShowLoginModal(false)} />
        </Modal>
      )}
    </>
  );
};

export default Header;
