import { API_URL } from "../utils/env";
import { IEventWithVenueTickets } from "../Interfaces/event-ticket.interface";
import axiosInstance from "./axiosInstance";

export const getTicketDetails = async (
  eventId: string
): Promise<IEventWithVenueTickets> => {
  try {
    const res = await axiosInstance.get(
      `${API_URL}/ticket/ticket-details/${eventId}`
    );
    return res.data;
  } catch (error: any) {
    console.error("Error fetching carousal data:", error);
    throw new Error(
      error?.response?.data?.message || "Failed to fetch carousal data"
    );
  }
};

export const getSelectedTicketsById = async (venueId: string) => {
  try {
    const res = await axiosInstance.get(
      `${API_URL}/users/cart-item/${venueId}`
    );
    return res.data;
  } catch (error: any) {
    console.error("Error fetching carousal data:", error);
    throw new Error(
      error?.response?.data?.message || "Failed to fetch carousal data"
    );
  }
};
