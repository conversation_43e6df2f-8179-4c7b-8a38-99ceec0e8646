import React from "react";
import { IVenueTicket } from "../../Interfaces/event-ticket.interface";
import "./SelectLocstion.css";

interface SelectLocationProps {
  venues: IVenueTicket[];
  selectedVenue: IVenueTicket | null;
  onSelect: (venue: IVenueTicket) => void;
}

const SelectLocation: React.FC<SelectLocationProps> = ({
  venues,
  selectedVenue,
  onSelect,
}) => {
  const uniqueVenues = Array.from(
    new Map(venues.map((v) => [v.venue, v])).values()
  );

  return (
    <div className="step-container">
      <div className="location-card-list">
        {uniqueVenues.length === 0 && <p>No venues available</p>}
        {uniqueVenues.map((venueData) => (
          <div
            key={venueData.venue}
            className={`location-card ${
              selectedVenue?.venue === venueData.venue ? "selected" : ""
            }`}
            onClick={() => onSelect(venueData)}
            aria-pressed={selectedVenue?.venue === venueData.venue}
          >
            <div className="radio-column">
              <input
                type="radio"
                checked={selectedVenue?.venue === venueData.venue}
                readOnly
              />
            </div>
            <div className="info-column">
              <div className="venue-name">{venueData.venue}</div>
              <div className="venue-address">
                {venueData.address ||
                  "123, axysadfgd, dgdfasad  ,near dsdfsfe 123, axysadfgd, dgdfasad  ,near dsdfsfe ,  123, axysadfgd, dgdfasad  ,near dsdfsfe  "}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SelectLocation;
