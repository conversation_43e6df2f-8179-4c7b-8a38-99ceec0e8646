:root {
  --main-border-radius: 12px;
}

.assign-container {
  padding: 30px 70px;
  background-color: var(--light-background-color);
  min-height: 100vh;
  animation: fadeIn 0.8s ease-in-out;
  padding-top: 114px !important;
  position: relative;
  z-index: 1;
}

.assign-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.assign-title {
  font-size: 32px;
  font-weight: bold;
  color: var(--primary-color);
  animation: bounceIn 0.8s ease-in-out;
  display: flex;
  align-items: center;
  gap: 10px;
}

.assign-icons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.assign-qr-icon {
  font-size: 24px;
  color: var(--primary-color);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.assign-qr-icon:hover {
  transform: scale(1.2);
}

.assign-ticket-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 30px;
}

.assign-ticket-list {
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--main-border-radius);
  padding: 20px;
  backdrop-filter: blur(10px);
  animation: fadeInUp 0.6s ease-in-out;
}

.assign-subtitle {
  font-size: 24px;
  margin-bottom: 20px;
}

/* === Event and Venue Blocks === */
.assign-event-block {
  background-color: var(--background-color);
  border-radius: var(--main-border-radius);
  padding: 20px;
  margin-bottom: 30px;
}

.assign-event-title {
  font-size: 22px;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 4px;
}

.assign-event-date {
  font-size: 14px;
  color: var(--text-muted);
  margin-bottom: 16px;
}

.assign-venue-block {
  background-color: var(--light-background-color);
  border-radius: var(--main-border-radius);
  padding: 15px;
  margin-bottom: 16px;
}

.assign-venue-title {
  font-size: 16px;
  color: var(--text-dark);
  font-weight: 500;
  margin-bottom: 12px;
}

/* === Ticket Card === */
.assign-ticket-card {
  background-color: var(--background-color);
  border-radius: var(--main-border-radius);
  padding: 15px;
  margin-bottom: 15px;
  transition: all 0.3s ease-in-out;
}

.assign-ticket-card:hover {
  border-color: var(--primary-color);
}

.assign-ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.assign-ticket-header h4 {
  font-size: 20px;
  color: var(--text-color);
  font-weight: 700;
}

.assign-ticket-status {
  color: white !important;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 500;
}

.status-confirmed {
  background-color: var(--btn-success);
}

.status-pending {
  background-color: var(--btn-warning);
}

.status-cancelled {
  background-color: var(--btn-danger);
}

.status-refunded {
  background-color: #6c757d;
}

.assign-ticket-card p {
  font-size: 14px;
  color: var(--text-light);
  margin-bottom: 4px;
}

/* === QR Code Dialog === */
.assign-dialog-overlay {
  position: fixed;
  inset: 0;
  background: var(--overlay-color);
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease-in-out;
  z-index: 1000;
}

.assign-dialog-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) !important;
  background: var(--background-color);
  border-radius: var(--main-border-radius);
  padding: 30px;
  max-width: 400px;
  width: 100%;
  z-index: 1001;
  animation: slideUp 0.3s ease-in-out;
}

.assign-qr-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 20px;
  text-align: center;
}

.assign-qr-image {
  display: block;
  margin: 0 auto;
  max-width: 250px;
  border-radius: var(--main-border-radius);
  height: auto;
}

/* === Animations === */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translate(-50%, -40%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  80% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@media (max-width: 768px) {
  .assign-header {
    padding: 20px;
  }
  .assign-title {
    font-size: 24px;
  }
  .assign-container {
    padding: 0;
  }
}
