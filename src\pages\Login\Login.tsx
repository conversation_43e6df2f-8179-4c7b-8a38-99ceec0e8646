import "./Login.css";
import React, { useState } from "react";
import { useAppDispatch } from "../../store/hooks";
import { setProfileName, verifyOTP } from "../../store/slices/user-slice";
import { isOtpCheck } from "../../services/user.service";
import countryData from "../../utils/mobile-country.json";
import Input from "../../components/Input/Input";
import axiosInstance from "../../services/axiosInstance";
import { useNavigate } from "react-router-dom";
import { RoutePaths } from "../../utils/route.enm";

interface Props {
  onClose?: () => void;
  isEventPopup?: boolean;
}

const Login: React.FC<Props> = ({ onClose, isEventPopup }) => {
  const [selectedCountry, setSelectedCountry] = useState("IN");
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [phone, setPhone] = useState("");
  const [otp, setOtp] = useState<string[]>(Array(6).fill(""));
  const [error, setError] = useState("");
  const [otpSent, setOtpSent] = useState(false);
  const [showProfileForm, setShowProfileForm] = useState(false);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);

  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const countryList = Object.entries(countryData).map(([key, value]: any) => ({
    code: key,
    name: value.name,
    prefix: value.prefix,
    iso2: value.iso2,
  }));

  const selectedPrefix =
    countryData?.[selectedCountry as keyof typeof countryData]?.prefix || "91";
  const isValidPhone = /^[0-9]{10}$/.test(phone);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isValidPhone)
      return setError("Please enter a valid 10-digit phone number.");

    if (!agreedToTerms) {
      return setError("You must agree to the terms and policies.");
    }
    try {
      setLoading(true);
      const res = await isOtpCheck(phone, selectedPrefix);
      if (res.sendOtp) {
        setOtpSent(true);
        setError("");
      } else {
        setError((res.payload as any)?.message || "Login failed.");
      }
    } catch {
      setError("Something went wrong. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handlePhoneChange = (value: string) => {
    if (/^\d{0,10}$/.test(value)) setPhone(value);
  };

  const handleOtpChange = (index: number, value: string) => {
    if (!/^\d?$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 5) {
      const next = document.querySelector<HTMLInputElement>(
        `.otp-box[data-index="${index + 1}"]`
      );
      next?.focus();
    }

    if (newOtp.every((d) => d !== "")) {
      setLoading(true);
      dispatch(verifyOTP({ phone, otp: newOtp.join("") }))
        .unwrap()
        .then((res) => {
          if (!res.user.firstName) {
            setShowProfileForm(true);
          } else {
            if (onClose) {
              onClose();
            }

            if (!onClose || !isEventPopup) {
              navigate("/");
            }

            setError("");
          }
        })
        .catch((err) => setError(err || "OTP verification failed."))
        .finally(() => setLoading(false));
    }
  };

  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && otp[index] === "" && index > 0) {
      const prev = document.querySelector<HTMLInputElement>(
        `.otp-box[data-index="${index - 1}"]`
      );
      prev?.focus();
    }

    if (e.key === "Enter") e.preventDefault();
  };

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!firstName || !lastName || !email)
      return setError("All fields are required.");

    try {
      setLoading(true);
      const data = { firstName, lastName, email, phone };
      dispatch(setProfileName(data));
      await axiosInstance.put("/users/profile", data);
      if (onClose) {
        onClose();
      }

      if (!onClose || !isEventPopup) {
        navigate("/");
      }
      setError("");
    } catch {
      setError("Could not update profile.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-wrapper">
      <div className="login-left">
        <div className="glow"></div>

        {!showProfileForm ? (
          <form className="login-form" onSubmit={handleSubmit}>
            {!otpSent ? (
              <>
                <h2 className="login-title">Welcome</h2>
                <div className="phone-input-group">
                  <select
                    className="country-select"
                    value={selectedCountry}
                    onChange={(e) => setSelectedCountry(e.target.value)}
                  >
                    {countryList.map((country) => (
                      <option key={country.code} value={country.code}>
                        +{country.prefix}
                      </option>
                    ))}
                  </select>
                  <Input
                    className="phone-input"
                    type="tel"
                    name="phone"
                    placeholder="Enter your phone number"
                    value={phone}
                    onChange={(e) => handlePhoneChange(e.target.value)}
                    maxLength={10}
                    required
                  />
                </div>

                <div className="terms-checkbox">
                  <input
                    type="checkbox"
                    id="terms"
                    checked={agreedToTerms}
                    onChange={() => setAgreedToTerms((prev) => !prev)}
                  />
                  <label htmlFor="terms">
                    I agree to the{" "}
                    <a
                      href={`${RoutePaths.TermsConditions}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Terms & Conditions
                    </a>{" "}
                    &amp;{" "}
                    <a
                      href={`${RoutePaths.PrivacyPolicy}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Privacy Policy
                    </a>
                  </label>
                </div>

                <button
                  type="submit"
                  disabled={!phone.length || loading}
                  className={`login-btn ${
                    !phone.length || loading ? "disabled" : ""
                  }`}
                >
                  {loading ? "Sending..." : "Let's Pravesh"}
                </button>
                {error && <p className="error-text">{error}</p>}
              </>
            ) : (
              <div className="otp-section">
                <p className="otp-instruction">
                  Enter the 6-digit OTP sent to your number
                </p>
                <div className="otp-inputs">
                  {otp.map((digit, index) => (
                    <input
                      key={index}
                      data-index={index}
                      type="number"
                      className="otp-box"
                      maxLength={1}
                      value={digit}
                      onChange={(e) => handleOtpChange(index, e.target.value)}
                      onKeyDown={(e) => handleOtpKeyDown(index, e)}
                      autoFocus={index === 0}
                    />
                  ))}
                  {loading && (
                    <div className="global-loader-overlay">
                      <div className="global-spinner" />
                    </div>
                  )}
                </div>
                {error && <p className="error-text">{error}</p>}
              </div>
            )}
          </form>
        ) : (
          <form className="login-form" onSubmit={handleProfileSubmit}>
            <h3 className="profile-title">Complete Your Profile</h3>
            <Input
              name="firstName"
              placeholder="First Name"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              required
            />
            <Input
              name="lastName"
              placeholder="Last Name"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              required
            />
            <Input
              name="email"
              placeholder="Email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
            <button className="login-btn" type="submit" disabled={loading}>
              {loading ? "Submitting..." : "Submit"}
            </button>
            {error && <p className="error-text">{error}</p>}
          </form>
        )}
      </div>

      <div className="login-right">
        <div className="login-overlay-content">
          <img src="/pravesh_round-white_logo.png" alt="Pravesh Logo" />
          <p className="login-overlay-subtext">
            Every celebration begins with Pravesh.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Login;
