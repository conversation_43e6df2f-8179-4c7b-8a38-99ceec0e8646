import { IVenueTicket } from "./event-ticket.interface";

export interface ICartItem {
  ticket: string;
  quantity: number;
}

export interface IVenueCartItem {
  [venueId: string]: {
    [ticketId: string]: number;
  }[];
}

export interface ICart extends Document {
  user: string;
  items: IVenueCartItem[];
}

export interface ICartEventTicket extends IVenueTicket {
  eventName: string;
  eventId: string;
}
