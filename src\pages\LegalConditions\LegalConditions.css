/* Import CSS Custom Properties */
:root {
  /* Primary Colors */
  --primary-color: #e63a4a;
  --secondary-color: #e63d51;
  --accent-secondary: #ff5e7a;
  --accent-tertiary: #b73555;
  /* Text Colors */
  --text-color: #333333;
  --text-light: #666666;
  --text-dark: #1a1a1a;
  /* Background Colors */
  --background-color: #fff;
  --background-dark: #e0e0e0;
  --light-background-color: #f5f5f5;
  --dark-mode-bg: #121212;
  --highlight-background: #fff3f4;
  /* Button Colors */
  --btn-text-color: #ffffff;
  --btn-primary: #e63a4a;
  --btn-secondary: #ff7d7d;
  --btn-danger: #d32f2f;
  --btn-success: #388e3c;
  --btn-info: #1976d2;
  --btn-warning: #f57c00;
  --text-muted: #888888;
  /* Border and Shadow */
  --border-color: #ddd;
  --border-dark: #999;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-dark: rgba(0, 0, 0, 0.2);
  /* Gradients */
  --gradient-primary: linear-gradient(45deg, #e63a4a, #b73555);
  --gradient-secondary: linear-gradient(45deg, #ff7d7d, #e63a4a);
  --gradient-accent: linear-gradient(45deg, #b73555, #ff5e7a);
  /* Special Colors */
  --overlay-color: rgba(0, 0, 0, 0.5);
  --muted-color: hsl(0, 0%, 70%);
  --highlight-color: #ffeb3b;
  --main-border-radius: 5px;
}

/* Modern Legal Page Styles */
* {
  box-sizing: border-box;
}

.legal-page {
  min-height: 100vh;
  background: linear-gradient(
    135deg,
    var(--light-background-color) 0%,
    var(--background-dark) 100%
  );
  padding: 2rem 1rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
}

/* Header Section */
.legal-header {
  max-width: 900px;
  margin: 0 auto 3rem auto;
  text-align: center;
  padding: 3rem 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 1.5rem;
  box-shadow: 0 20px 40px var(--shadow-light);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.legal-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.legal-header h1 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.02em;
}

.legal-header .date {
  font-size: 1rem;
  color: var(--text-light);
  font-weight: 500;
  margin: 0;
  padding: 0.5rem 1rem;
  background: var(--light-background-color);
  border-radius: 2rem;
  display: inline-block;
  border: 1px solid var(--border-color);
}

/* Content Container */
.legal-content {
  max-width: 900px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px var(--shadow-dark);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

/* Introduction Section */
.legal-intro {
  padding: 2.5rem;
  background: linear-gradient(
    135deg,
    var(--light-background-color) 0%,
    var(--highlight-background) 100%
  );
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.legal-intro::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 2.5rem;
  right: 2.5rem;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: 1px;
}

.legal-intro p {
  font-size: 1.125rem;
  color: var(--text-color);
  margin: 0;
  font-weight: 400;
  text-align: center;
}

/* Sections */
.legal-content section {
  padding: 20px;
  border-bottom: 1px solid var(--light-background-color);
  position: relative;
  transition: all 0.3s ease;
}

.legal-content section:last-child {
  border-bottom: none;
}

.legal-content section:hover {
  background: rgba(255, 243, 244, 0.5);
}

.legal-disclaimer h2 {
  color: var(--accent-tertiary) !important;
  font-size: 1.375rem !important;
  margin-bottom: 1.5rem !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legal-disclaimer h2::before {
  content: "📋";
  font-size: 1.25rem;
}

.disclaimer-content p {
  color: var(--text-dark) !important;
  font-weight: 500;
  margin-bottom: 1.25rem;
  line-height: 1.7;
}

.disclaimer-warning {
  background: rgba(230, 58, 74, 0.1) !important;
  border: 1px solid var(--accent-secondary) !important;
  border-radius: var(--main-border-radius) !important;
  padding: 1rem !important;
  color: var(--btn-danger) !important;
  font-weight: 600 !important;
  position: relative;
}

.disclaimer-warning::before {
  content: "⚠️";
  margin-right: 0.5rem;
}

/* Section Numbers */
.legal-content section h2::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background: var(--gradient-primary);
  border-radius: 2px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.legal-content section:hover h2::before {
  opacity: 1;
}

/* Override for disclaimer section */
.legal-disclaimer h2::before {
  display: none !important;
}

/* Typography */
.legal-content h2 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--text-dark);
  position: relative;
  padding-left: 1rem;
  letter-spacing: -0.01em;
}

.legal-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 2rem 0 1rem 0;
  color: var(--text-color);
  position: relative;
  padding-left: 0.75rem;
}

.legal-content h3::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 1.25rem;
  background: var(--gradient-primary);
  border-radius: 2px;
}

.legal-content p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
  color: var(--text-color);
  font-size: 1rem;
}

/* Lists */
.legal-content ul {
  list-style: none;
  padding-left: 0;
  margin-bottom: 1.5rem;
}

.legal-content ul li {
  margin-bottom: 0.75rem;
  padding-left: 2rem;
  position: relative;
  color: var(--text-color);
  line-height: 1.7;
  transition: all 0.2s ease;
}

.legal-content ul li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 6px;
  height: 6px;
  background: var(--gradient-primary);
  border-radius: 50%;
  transform: translateY(-50%);
}

.legal-content ul li:hover {
  color: var(--text-dark);
}

.legal-content ul li:hover::before {
  width: 8px;
  height: 8px;
  box-shadow: 0 0 0 3px rgba(230, 58, 74, 0.2);
}

/* Links */
.legal-content a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
  border-bottom: 1px solid transparent;
}

.legal-content a:hover {
  color: var(--accent-tertiary);
  border-bottom-color: var(--accent-tertiary);
}

/* Contact Section Special Styling */
.legal-content section:nth-last-child(2) {
  background: linear-gradient(
    135deg,
    var(--light-background-color) 0%,
    var(--highlight-background) 100%
  );
}

.legal-content section:nth-last-child(2) p {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.legal-content section:nth-last-child(2) a {
  background: var(--gradient-primary);
  color: var(--btn-text-color);
  padding: 0.5rem 1rem;
  border-radius: var(--main-border-radius);
  text-decoration: none;
  transition: all 0.3s ease;
  border-bottom: none;
}

.legal-content section:nth-last-child(2) a:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(230, 58, 74, 0.3);
  color: var(--btn-text-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .legal-page {
    padding: 1rem 0.5rem;
  }

  .legal-header {
    margin-bottom: 2rem;
    padding: 2rem 1.5rem;
    border-radius: 1rem;
  }

  .legal-header h1 {
    font-size: 2.25rem;
  }

  .legal-content {
    border-radius: 1rem;
  }

  .legal-intro {
    padding: 2rem 1.5rem;
  }

  .legal-intro::after {
    left: 1.5rem;
    right: 1.5rem;
  }

  .legal-content section {
    padding: 20px 6px;
  }

  .legal-content h2 {
    font-size: 1.375rem;
  }

  .legal-content h3 {
    font-size: 1.125rem;
  }

  .legal-disclaimer::before {
    top: 0.75rem;
    right: 1rem;
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .legal-header {
    padding: 1.5rem 1rem;
  }

  .legal-header h1 {
    font-size: 1.875rem;
  }

  .legal-intro {
    padding: 1.5rem 1rem;
  }

  .legal-intro::after {
    left: 1rem;
    right: 1rem;
  }

  .legal-content section {
    padding: 0.75rem 2.5rem;
  }

  .legal-content section:nth-last-child(2) p {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .legal-content section:nth-last-child(2) a {
    align-self: stretch;
    text-align: center;
  }

  .legal-disclaimer {
    margin: 1.5rem 0 !important;
  }

  .legal-disclaimer::before {
    display: none;
  }
}

/* Print Styles */
@media print {
  .legal-page {
    background: white;
    padding: 1rem;
  }

  .legal-header,
  .legal-content {
    background: white;
    box-shadow: none;
    border: 1px solid var(--border-color);
  }

  .legal-header h1 {
    color: var(--text-dark);
    -webkit-text-fill-color: var(--text-dark);
  }

  .legal-content section:hover {
    background: transparent;
  }

  .legal-disclaimer {
    background: var(--light-background-color) !important;
    border: 2px solid var(--border-dark) !important;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .legal-page {
    background: white;
  }

  .legal-header,
  .legal-content {
    background: white;
    border: 2px solid #000;
  }

  .legal-header h1 {
    color: #000;
    -webkit-text-fill-color: #000;
  }

  .legal-content h2,
  .legal-content h3,
  .legal-content p,
  .legal-content li {
    color: #000;
  }

  .legal-content a {
    color: #0000ee;
  }

  .legal-disclaimer {
    background: #fff !important;
    border: 3px solid #000 !important;
  }

  .disclaimer-content p,
  .disclaimer-warning {
    color: #000 !important;
  }
}

/* Focus Management */
.legal-content section:focus-within {
  background: rgba(255, 243, 244, 0.8);
}

.legal-content section:focus-within h2::before {
  opacity: 1;
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Selection Styling */
::selection {
  background: rgba(230, 58, 74, 0.2);
  color: var(--text-dark);
}

::-moz-selection {
  background: rgba(230, 58, 74, 0.2);
  color: var(--text-dark);
}

/* Additional Disclaimer Styling to match your original */
.legal-disclaimer {
  font-size: 14px;
  color: var(--text-dark);
  background: linear-gradient(
    135deg,
    var(--highlight-background) 0%,
    #ffebee 100%
  ) !important;
  padding: 20px;
  border-radius: var(--main-border-radius);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
}

/* Enhanced styling for better visual hierarchy */
.legal-disclaimer h2 {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* Hover effects for interactive elements */
.legal-content section {
  cursor: default;
}

.legal-content section:hover {
  background: var(--highlight-background);
}

/* Enhanced button styling */
.legal-content section:nth-last-child(2) a {
  background: var(--gradient-primary);
  box-shadow: 0 4px 15px rgba(230, 58, 74, 0.2);
  font-weight: 600;
}

.legal-content section:nth-last-child(2) a:hover {
  background: var(--gradient-secondary);
  box-shadow: 0 8px 25px rgba(230, 58, 74, 0.4);
}

/* Enhanced list styling */
.legal-content ul li {
  transition: all 0.3s ease;
}

.legal-content ul li:hover {
  background: rgba(255, 243, 244, 0.3);
  border-radius: var(--main-border-radius);
}
