.card-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.card {
  width: 300px;
  background-color: var(--background-color);
  overflow: hidden;
  cursor: pointer;
  border-radius: 12px;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transform: scale(1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
}

.card:hover {
  transform: scale(1.03);
  box-shadow: 0 12px 30px var(--shadow-light, rgba(0, 0, 0, 0.1));
}

.card-image {
  width: 100%;
  height: 350px;
  object-fit: cover;
  display: block;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  transition: transform 0.4s ease;
}

.card:hover .card-image {
  transform: scale(1.05);
}

.card-body {
  padding: 16px;
}

.card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 8px;
}

.card-clamped-description {
  font-size: 0.95rem;
  line-height: 150%;
  color: var(--text-light);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Fade-in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media only screen and (max-width: 600px) {
  .card {
    width: 170px;
  }
  .card-image {
    height: 210px;
  }
}

@media (min-width: 600px) {
  .card {
    width: 200px;
  }
  .card-image {
    height: 280px;
  }
}

@media (min-width: 768px) {
  .card {
    width: 250px;
  }
  .card-image {
    height: 350px;
  }
}
