.ticket-card-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ticket-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  background-color: #f9f9f9;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.02);
  cursor: pointer;
  gap: 1rem;
  transition: all 0.3s ease;
  margin-bottom: 0px;
}

.ticket-card:hover {
  background-color: #ffefef;
  transform: translateY(-2px);
  border-color: var(--border-color);
}

.ticket-card.selected {
  border-color: var(--primary-color);
  background-color: var(--selected-bg);
}

.ticket-info {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.ticket-name {
  font-weight: 700;
  font-size: 1.15rem;
  color: var(--text-dark);
}

.ticket-price {
  /* color: #6b7280; */
  color: #e63a4a;
  font-weight: 500;
  font-size: 15px;
  text-align: left;
  margin-top: 5px;
}

.ticket-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}
.ticket-date-heading {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: left;
}

.ticket-available {
  font-size: 14px;
  color: #ffffff;
  background-color: var(--primary-color);
  padding: 4px 10px;
  border-radius: 999px;
  font-weight: 500;
}

.ticket-counter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 7px;
  padding: 4px 10px;
  font-weight: 600;
  border: 1px solid var(--border-dark);
  background-color: #fdfdfd;
}

.ticket-counter input {
  border: none !important;
}

.counter-btn {
  border: none;
  font-size: 20px;
  font-weight: 600;
  cursor: pointer;
  color: var(--primary-color);
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: 25px;
  transition: background 0.2s ease;
  user-select: none;
}

.counter-btn:hover {
  background-color: #ffe4e6;
  border-radius: 6px;
}

.counter-input {
  width: 40px;
  height: 25px;
  text-align: center;
  font-size: 1rem;
  border: 1px solid var(--border-dark);
  border-radius: 0.5rem;
  padding: 0.25rem;
  background-color: #fff;
  color: var(--text-dark);
  padding: 0 !important;
  margin-bottom: -19px !important;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

@media only screen and (max-width: 600px) {
  .ticket-counter {
    padding: 4px;
  }

  .header {
    padding: 15px 25px;
  }

  .header h4 {
    font-size: 24px;
  }

  .ticket-available {
    font-size: 12px;
  }
}

@media only screen and (max-width: 600px) {
  .ticket-card {
    margin-left: 8px;
  }
}
