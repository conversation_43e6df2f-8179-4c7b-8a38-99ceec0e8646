import "./Loader.css";
import React from "react";
import <PERSON><PERSON> from "lottie-react";
import animationData from "../../assets/loader.json";

const Loader: React.FC = () => {
  return (
    <div className="loader-container">
      <Lottie
        animationData={animationData}
        loop={true}
        style={{ width: 300, height: 300 }}
      />
      <div className="loader-text">Pravesh</div>
    </div>
  );
};

export default Loader;
