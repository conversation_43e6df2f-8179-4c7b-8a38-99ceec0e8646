const ContactUsPage = () => {
  return (
    <main className="legal-page">
      <header className="legal-header">
        <h1>Contact Us – Pravesh</h1>
      </header>

      <div className="legal-content">
        <section className="legal-intro">
          <p>
            We're here to help you with all your event ticketing needs. Whether
            you have a question about your booking, want to report an issue, or
            just want to connect with us – don’t hesitate to reach out. Our
            support team is always ready to assist you.
          </p>
        </section>

        <section>
          <h2>Email Support</h2>
          <ul>
            <li>
              For all inquiries, support requests, or partnership opportunities:
              <br />
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </li>
            <li>
              <strong>Response Time:</strong> Within 24–48 business hours
            </li>
          </ul>
        </section>

        <section>
          <h2>Phone Support</h2>
          <p>
            You can also reach us directly at:{" "}
            <a href="tel:+919023658437">90236 58437</a>
          </p>
        </section>

        <section>
          <h2>Official Website</h2>
          <p>
            Visit us online for event listings, updates, and help resources:
            <br />
            <a
              href="https://www.pravesh.events"
              target="_blank"
              rel="noopener noreferrer"
            >
              www.pravesh.events
            </a>
          </p>
        </section>

        <section>
          <h2>Customer Support Hours</h2>
          <ul>
            <li>Monday to Saturday: 10:00 AM – 6:00 PM (IST)</li>
            <li>Sunday: Closed</li>
          </ul>
        </section>

        <section>
          <h2>For Event Organizers</h2>
          <p>
            Interested in hosting your event with us?
            <br />
            Email: <a href="mailto:<EMAIL>"><EMAIL></a>
            <br />
            We'll get in touch to help you onboard your event with ease.
          </p>
        </section>
      </div>
    </main>
  );
};

export default ContactUsPage;
