import "./Carousel.css";
import { useRef, useEffect, useState } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay, Mousewheel } from "swiper/modules";
import { ICarousalData } from "../../Interfaces/organization.interface";
import { useNavigate } from "react-router-dom";
import { Icons } from "../Icons/Icons";
import moment from "moment";

// Import Swiper styles
import "swiper/swiper-bundle.css";

interface Props {
  items: ICarousalData[];
}

const HeroCarousel = ({ items }: Props) => {
  const navigate = useNavigate();
  const swiperRef = useRef<any>(null);
  const [progress, setProgress] = useState(0);

  const handleNavigation = (slug: string) => {
    navigate(`/event-details/${slug}`);
  };

  const formatDate = (dateString: string) => {
    return moment(dateString).format("MMM DD, YYYY");
  };

  const handleAutoplayTimeLeft = (_s: any, _time: number, progress: number) => {
    setProgress((1 - progress) * 100);
  };

  const formatDuration = (duration: string) => {
    // Assuming duration is in format like "2 hours" or "3 days"
    return duration;
  };

  useEffect(() => {
    // Custom autoplay control
    const handleMouseEnter = () => {
      if (swiperRef.current?.swiper) {
        swiperRef.current.swiper.autoplay.stop();
      }
    };

    const handleMouseLeave = () => {
      if (swiperRef.current?.swiper) {
        swiperRef.current.swiper.autoplay.start();
      }
    };

    const swiperElement = swiperRef.current;
    if (swiperElement) {
      swiperElement.addEventListener("mouseenter", handleMouseEnter);
      swiperElement.addEventListener("mouseleave", handleMouseLeave);

      return () => {
        swiperElement.removeEventListener("mouseenter", handleMouseEnter);
        swiperElement.removeEventListener("mouseleave", handleMouseLeave);
      };
    }
  }, []);

  if (!items || items.length === 0) {
    return null;
  }

  return (
    <div
      className="hero-carousel-wrapper"
      style={{
        marginTop: "4rem",
      }}
    >
      <Swiper
        ref={swiperRef}
        modules={[Navigation, Pagination, Autoplay, Mousewheel]}
        spaceBetween={30}
        slidesPerView={3}
        centeredSlides={true}
        navigation={{
          nextEl: ".hero-carousel-next",
          prevEl: ".hero-carousel-prev",
        }}
        pagination={{
          clickable: true,
          dynamicBullets: true,
          dynamicMainBullets: 3,
        }}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true,
        }}
        breakpoints={{
          320: {
            slidesPerView: 1.2,
            spaceBetween: 20,
            centeredSlides: true,
          },
          768: {
            slidesPerView: 2.5,
            spaceBetween: 25,
            centeredSlides: true,
          },
          1024: {
            slidesPerView: 3,
            spaceBetween: 30,
            centeredSlides: true,
          },
        }}
        speed={800}
        loop={true}
        grabCursor={true}
        touchRatio={1}
        simulateTouch={true}
        allowTouchMove={true}
        mousewheel={{
          forceToAxis: true,
        }}
        onAutoplayTimeLeft={handleAutoplayTimeLeft}
        className="preview-carousel"
      >
        {items.map((item, index) => (
          <SwiperSlide key={item._id || index} className="hero-slide">
            <div className="hero-slide-bg">
              <img
                src={item.bannerImage}
                alt={item.name}
                className="hero-slide-image"
                data-swiper-parallax="-300"
              />
              <div className="hero-slide-overlay"></div>
            </div>

            <div className="hero-slide-content" data-swiper-parallax="-100">
              <div className="hero-content-container">
                <div className="hero-content-main">
                  <div className="hero-badge">
                    <span className="hero-badge-text">Featured Event</span>
                  </div>

                  <h1 className="hero-title" data-swiper-parallax="-200">
                    {item.name}
                  </h1>

                  <div className="hero-meta" data-swiper-parallax="-150">
                    <div className="hero-meta-item">
                      <Icons.Calendar className="hero-meta-icon" />
                      <span>{formatDate(item.startDate)}</span>
                    </div>
                    <div className="hero-meta-item">
                      <Icons.MapPin className="hero-meta-icon" />
                      <span>{item.location}</span>
                    </div>
                    <div className="hero-meta-item">
                      <Icons.Clock className="hero-meta-icon" />
                      <span>{formatDuration(item.duration)}</span>
                    </div>
                  </div>

                  <div className="hero-actions" data-swiper-parallax="-100">
                    <button
                      className="hero-btn hero-btn-primary"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleNavigation(item.slug);
                      }}
                    >
                      <span>Book Now</span>
                      <Icons.ArrowRight className="hero-btn-icon" />
                    </button>

                    <button
                      className="hero-btn hero-btn-secondary"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleNavigation(item.slug);
                      }}
                    >
                      <span>Learn More</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Navigation */}
      <div className="hero-carousel-navigation">
        <button className="hero-carousel-prev hero-nav-btn">
          <Icons.ChevronLeft />
        </button>
        <button className="hero-carousel-next hero-nav-btn">
          <Icons.ChevronRight />
        </button>
      </div>

      <div className="carousel-dots">
          {items.map((_, index) => (
            <span
              key={index}
              className={`dot ${index === currentIndex ? "active" : ""}`}
              onClick={() => {
                setCurrentIndex(index);
                scrollToIndex(index);
              }}
            />
          ))}
        </div>

      {/* Progress Bar */}
      {/* <div className="hero-progress-bar">
        <div
          className="hero-progress-fill"
          style={{ width: `${progress}%` }}
        ></div>
      </div> */}
    </div>
  );
};

export default HeroCarousel;
