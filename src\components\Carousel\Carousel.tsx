import "./Carousel.css";
import { useEffect, useRef, useState } from "react";
import { ICarousalData } from "../../Interfaces/organization.interface";
import { useNavigate } from "react-router-dom";
import { Icons } from "../Icons/Icons";

interface Props {
  items: ICarousalData[];
}

const Carousel = ({ items }: Props) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const totalSlides = items.length;

  const navigator = useNavigate();

  const scrollToIndex = (index: number) => {
    const container = containerRef.current;
    if (container) {
      requestAnimationFrame(() => {
        const slide = container.querySelector(".carousel-slide") as HTMLElement;
        const slideWidth = slide?.offsetWidth || 0;
        if (!slideWidth) return;
        container.scrollTo({
          left: slideWidth * (index + 1),
          behavior: "smooth",
        });
      });
    }
  };
  useEffect(() => {
    const handleResize = () => {
      scrollToIndex(currentIndex);
    };
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [currentIndex]);

  useEffect(() => {
    if (!containerRef.current || items.length === 0) return;

    const initializeScroll = () => {
      const container = containerRef.current!;
      const slide = container.querySelector(".carousel-slide") as HTMLElement;
      const slideWidth = slide?.offsetWidth || 0;
      container.scrollLeft = slideWidth;
    };

    setTimeout(() => {
      initializeScroll();
    }, 100);

    intervalRef.current = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % totalSlides;
        scrollToIndex(nextIndex);
        return nextIndex;
      });
    }, 3000);

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [items.length]);

  const handleScroll = () => {
    const container = containerRef.current;
    if (!container) return;

    const slide = container.querySelector(".carousel-slide") as HTMLElement;
    const slideWidth = slide?.offsetWidth || 0;
    const scrollLeft = container.scrollLeft;

    if (scrollLeft <= 0) {
      container.scrollLeft = slideWidth * totalSlides;
      setCurrentIndex(totalSlides - 1);
      return;
    }

    if (scrollLeft >= slideWidth * (totalSlides + 1)) {
      container.scrollLeft = slideWidth;
      setCurrentIndex(0);
      return;
    }

    const rawIndex = Math.round(scrollLeft / slideWidth) - 1;
    const normalizedIndex = (rawIndex + totalSlides) % totalSlides;
    setCurrentIndex(normalizedIndex);
  };

  const handleNavigation = (slug: string) => {
    navigator(`/event-details/${slug}`);
  };

  return (
    <div className="carousel-wrapper">
      <div className="carousel-content">
        <div
          className="carousel-container"
          ref={containerRef}
          onScroll={handleScroll}
        >
          {/* Clone Last Slide at Start */}
          <div
            className="carousel-slide"
            key={"clone-last"}
            onClick={() => handleNavigation(items[items.length - 1].slug)}
          >
            <div className="image-section">
              <img src={items[items.length - 1].bannerImage} alt="last-clone" />
            </div>
            <div className="info-section">
              <h3 className="info-event-name">
                {items[items.length - 1].name}
              </h3>
              <button
                onClick={() => handleNavigation(items[items.length - 1].slug)}
              >
                More Details <Icons.LeftChevron fill="#E63A4A" />
              </button>
            </div>
          </div>

          {/* Actual Slides */}
          {items.map((item, index) => (
            <div
              className="carousel-slide"
              key={index}
              onClick={() => handleNavigation(item.slug)}
            >
              <div className="image-section">
                <img
                  src={item.bannerImage}
                  alt={item.slug || `Slide ${index}`}
                />
              </div>
              <div className="info-section">
                <h3 className="info-event-name">{item.name}</h3>
                <button
                  onClick={() => handleNavigation(items[items.length - 1].slug)}
                >
                  More Details <Icons.LeftChevron fill="#E63A4A" />
                </button>
              </div>
            </div>
          ))}

          {/* Clone First Slide at End */}
          <div
            className="carousel-slide"
            key={"clone-first"}
            onClick={() => handleNavigation(items[0].slug)}
          >
            <div className="image-section">
              <img src={items[0].bannerImage} alt="first-clone" />
            </div>
            <div className="info-section">
              <h3 className="info-event-name">{items[0].name}</h3>
              <button>
                More Details <Icons.LeftChevron fill="#E63A4A" />
              </button>
            </div>
          </div>
        </div>

        {/* Dots */}
        <div className="carousel-dots">
          {items.map((_, index) => (
            <span
              key={index}
              className={`dot ${index === currentIndex ? "active" : ""}`}
              onClick={() => {
                setCurrentIndex(index);
                scrollToIndex(index);
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Carousel;
