import {
  Fa<PERSON><PERSON><PERSON>,
  FaReg<PERSON>lock,
  FaShieldAlt,
  FaTicketAlt,
} from "react-icons/fa";
import "./HomeFooter.css";
import { RoutePaths } from "../../utils/route.enm";
import { Icons } from "../Icons/Icons";

const HomeFooter = () => {
  return (
    <footer className="footer fade-in">
      <div className="list-show-bar">
        <div className="list-show-content">
          <h2>Turn Your Vision Into Sold-Out Shows</h2>
          <p>
            Ready to showcase your incredible event to thousands? Join Pravesh
            and transform your creative passion into a thriving experience that
            audiences will remember forever.
          </p>
          <a href="mailto:<EMAIL>">
            <button className="list-show-btn">
              Start Listing Now <Icons.LeftChevron />
            </button>
          </a>
        </div>
      </div>

      <section className="trust-section">
        <h2>Your Trusted Ticket Partner</h2>
        <div className="trust-grid">
          <div className="trust-item">
            <FaShieldAlt className="trust-icon" />
            <h3>Secure Checkout</h3>
            <p>Fast & Secured Payment</p>
          </div>
          <div className="trust-item">
            <FaRegClock className="trust-icon" />
            <h3>Instant Confirmation</h3>
            <p>Refund guarantee options</p>
          </div>
          <div className="trust-item">
            <FaTicketAlt className="trust-icon" />
            <h3>Official Ticket Seller</h3>
            <p>Used by many people</p>
          </div>
          <div className="trust-item">
            <FaHeadset className="trust-icon" />
            <h3>24/7 Customer Service</h3>
            <p>Reliable after sales support</p>
          </div>
        </div>
      </section>

      <div className="footer-container">
        <div className="footer-column-container">
          <div className="footer-logo">
            <img src="/logo.png" alt="Logo" />
          </div>
          <div className="footer-column">
            <ul>
              <li>
                <a
                  href={`${RoutePaths.ContactUs}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Contact Us
                </a>
              </li>
              <li>
                <a
                  href={`${RoutePaths.TermsConditions}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Terms & Conditions
                </a>
              </li>
              <li>
                <a
                  href={`${RoutePaths.PrivacyPolicy}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Privacy Policy
                </a>
              </li>
              <li>
                <a
                  href={`${RoutePaths.RefundPolicy}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Refund Policy
                </a>
              </li>
              <li>
                <a
                  href={`${RoutePaths.CancellationPolicy}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Cancellation Policy
                </a>
              </li>
              <li>
                <a
                  href={`${RoutePaths.ShippingDeliveryPolicy}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Shipping & Delivery Policy
                </a>
              </li>
            </ul>
          </div>
        </div>

        <div className="footer-note">
          <p>© {new Date().getFullYear()} Pravesh. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};
export default HomeFooter;
