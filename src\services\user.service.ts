import { API_URL } from "../utils/env";
import { ICartEventTicket, IVenueCartItem } from "../Interfaces/cart.interface";
import axiosInstance from "./axiosInstance";

export const getCart = async (): Promise<ICartEventTicket[]> => {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) {
      throw new Error("Token not found");
    }
    const res = await axiosInstance.get(`/users/cart`);
    return res.data.cartData;
  } catch (error: any) {
    throw new Error(
      error?.response?.data?.message || "Failed to fetch carousal data"
    );
  }
};

export const addToCart = async (items: IVenueCartItem): Promise<void> => {
  try {
    const res = await axiosInstance.put(`${API_URL}/users/cart`, {
      items,
    });

    return res.data;
  } catch (error: any) {
    throw new Error(
      error?.response?.data?.message || "Failed to fetch carousal data"
    );
  }
};

export const isOtpCheck = async (phone: string, phonePrefix: string) => {
  try {
    const { data } = await axiosInstance.post(`/auth/login`, {
      phone,
      phonePrefix,
    });

    return data;
  } catch (error: any) {
    throw new Error(
      error?.response?.data?.message || "Failed to fetch carousal data"
    );
  }
};

export const saveTransaction = async (
  response: any,
  amount: number,
  selectedTickets: ICartEventTicket[]
) => {
  try {
    return await axiosInstance.post("payment/check-and-add-tickets", {
      razorpay_order_id: response.razorpay_order_id,
      razorpay_payment_id: response.razorpay_payment_id,
      razorpay_signature: response.razorpay_signature,
      selectedTickets,
      amount,
    });
  } catch (error: any) {
    throw new Error(
      error?.response?.data?.message || "Failed while add transaction"
    );
  }
};
