import "./Ticket.css";
import React, { useEffect, useState } from "react";
import {
  getSelectedTicketsById,
  getTicketDetails,
} from "../../services/event-ticket.service";
import {
  IEventTicket,
  ITicketsByDate,
  IVenueTicket,
} from "../../Interfaces/event-ticket.interface";
import { Link, useNavigate, useParams } from "react-router-dom";
import { IEvent } from "../../Interfaces/organization.interface";
import SelectLocation from "../../components/SelectLocation/SelectLocation";
import SelectTicket from "../../components/SelectTicket/SelectTicket";
import axiosInstance from "../../services/axiosInstance";
import { RoutePaths } from "../../utils/route.enm";
import Loader from "../../components/Loader/Loader";

const steps = ["Select Place", "Select Ticket"];

const Ticket: React.FC = () => {
  const [eventTicketData, setEventTicketData] = useState<IEventTicket>();
  const [ticketData, setTicketData] = useState<IVenueTicket[]>([]);
  const [selectedVenue, setSelectedVenue] = useState<IVenueTicket | null>(null);
  const [isSingleVenue, setIsSingleVenue] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [quantities, setQuantities] = useState<{ [key: string]: number }>({});
  const [isFreeTickets, setIsFreeTickets] = useState(false);
  const [loading, setLoading] = useState(false);

  const { id } = useParams<{ id: string }>();

  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      if (!id) return;
      setLoading(true);

      try {
        const data = await getTicketDetails(id);
        data.venueTickets.forEach((v) =>
          v.ticketTypes.forEach((t) => (t.venueId = v._id))
        );
        const uniqueVenues = Array.from(
          new Map(data.venueTickets.map((v) => [v.venue, v])).values()
        );
        setEventTicketData(data.eventTicket);
        setTicketData(data.venueTickets);
        const isSingle = uniqueVenues.length === 1;
        setIsSingleVenue(isSingle);

        if (isSingle) {
          setSelectedVenue(data.venueTickets[0]);
          await getSelectedTickets(data.venueTickets[0]._id);
          setCurrentStep(1);
        } else {
          setCurrentStep(0);
        }
      } catch (err) {
        console.error("Error fetching ticket details", err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  useEffect(() => {
    if (selectedVenue) {
      hasOnlyFreeTickets(selectedVenue, ticketData);
    }
  }, [selectedVenue, ticketData]);

  if (loading) {
    return <Loader />;
  }

  const getSelectedTickets = async (venueId: string) => {
    const data = await getSelectedTicketsById(venueId);
    if (data && data.selectedTickets) {
      setQuantities(data.selectedTickets);
    }
  };

  const getTicketsGroupedByDate = (
    venue: IVenueTicket | null,
    tickets: IVenueTicket[] = ticketData
  ): ITicketsByDate => {
    if (!venue) return {};

    const filteredTickets = tickets.filter((t) => t.venue === venue.venue);

    const grouped: ITicketsByDate = {};

    filteredTickets.forEach((t) => {
      const dateStr = new Date(t.date).toISOString().slice(0, 10);

      if (!grouped[dateStr]) {
        grouped[dateStr] = [];
      }

      grouped[dateStr] = grouped[dateStr].concat(t.ticketTypes);
    });

    return grouped;
  };

  const hasOnlyFreeTickets = (
    venue: IVenueTicket | null,
    ticketList: IVenueTicket[]
  ) => {
    if (!venue) return;

    const venueTickets = ticketList.filter((t) => t.venue === venue.venue);

    const allTicketTypes = venueTickets.flatMap((t) => t.ticketTypes);

    const allFree = allTicketTypes.every((ticket) => ticket.price === 0);

    setIsFreeTickets(allFree);
  };

  const goToStep = (step: number) => {
    if (step < 0 || step > steps.length - 1) return;
    setCurrentStep(step);
  };

  const nextStep = () => {
    if (currentStep === 0 && selectedVenue) {
      setCurrentStep(1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      goToStep(currentStep - 1);
    }
  };

  const handleFreeTicketBooking = async () => {
    console.log("Booking free tickets...", quantities);
    try {
      setLoading(true);
      await axiosInstance.post("/users/add-free-tickets", {
        venueId: selectedVenue?._id,
        eventId: id,
        items: quantities,
      });
      navigate(RoutePaths.Assign);
    } catch (error) {
      console.error("Error booking free tickets:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="booking-container">
      <div className="ticket-header">
        <div>
          <Link to="/" style={{ textDecoration: "none", color: "inherit" }}>
            <h4>Pravesh</h4>
          </Link>
        </div>
        {eventTicketData && (
          <div className="event-info">
            <h3>{(eventTicketData.event as IEvent).name}</h3>
          </div>
        )}
      </div>

      {!isSingleVenue && (
        <nav className="breadcrumb">
          {steps.map((label, idx) => (
            <div key={label} className="step">
              <span
                className={`step-label ${idx <= currentStep ? "active" : ""}`}
                style={{ cursor: idx < currentStep ? "pointer" : "default" }}
                onClick={() => {
                  if (idx < currentStep) goToStep(idx);
                }}
                aria-current={idx === currentStep ? "step" : undefined}
              >
                {label}
              </span>
              <span
                className={`line ${idx <= currentStep ? "active" : ""}`}
              ></span>
            </div>
          ))}
        </nav>
      )}

      {isSingleVenue && (
        <div>
          <div className="location-header">
            <h4>Select Ticket</h4>
            <p className="location-address">
              You can choose from the tickets available at this selected
              location
            </p>
          </div>
          <div className="location-container">
            <p className="location-title">{selectedVenue?.venue}</p>
            <p className="location-address">{selectedVenue?.address}</p>
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="ticket-main-content">
        {currentStep === 0 && (
          <SelectLocation
            venues={ticketData}
            selectedVenue={selectedVenue}
            onSelect={(venue) => {
              setSelectedVenue(venue);
              getSelectedTickets(venue._id);
            }}
          />
        )}

        {currentStep === 1 && selectedVenue && (
          <SelectTicket
            tickets={getTicketsGroupedByDate(selectedVenue)}
            quantities={quantities}
            setQuantities={setQuantities}
            isFreeTickets={isFreeTickets}
          />
        )}

        <div className="btn-container">
          {!isSingleVenue && (
            <button
              className="back-btn"
              onClick={prevStep}
              disabled={currentStep === 0}
              aria-label="Previous step"
            >
              Back
            </button>
          )}
          <button
            className="next-btn"
            onClick={
              currentStep === steps.length - 1
                ? isFreeTickets
                  ? handleFreeTicketBooking
                  : undefined
                : nextStep
            }
            disabled={currentStep === 0 && !selectedVenue}
            aria-label="Next step"
          >
            {currentStep === steps.length - 1 ? (
              isFreeTickets ? (
                loading ? (
                  "Booking..."
                ) : (
                  "Book Ticket"
                )
              ) : (
                <Link to="/cart">Goto Cart</Link>
              )
            ) : (
              "Next"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Ticket;
