/* Reset and Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Main Container */
.verify-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", sans-serif;
}

.verify-container {
  width: 100%;
  max-width: 28rem;
}

/* Card Styles */
.verify-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.verify-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 32px 64px -12px rgba(0, 0, 0, 0.35);
}

.verify-content {
  padding: 3rem 2rem;
  text-align: center;
}

/* Icon Section */
.icon-section {
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.icon-container {
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Spinner Animation */
.spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Success Checkmark */
.checkmark {
  width: 3rem;
  height: 3rem;
  position: relative;
}

.checkmark-circle {
  width: 100%;
  height: 100%;
  border: 3px solid #10b981;
  border-radius: 50%;
  position: relative;
  animation: checkmark-circle 0.6s ease-in-out;
}

.checkmark-stem {
  position: absolute;
  width: 4px;
  height: 20px;
  background-color: #10b981;
  left: 25px;
  top: 16px;
  transform: rotate(45deg);
  border-radius: 2px;
  animation: checkmark-stem 0.4s ease-in-out 0.2s both;
}

.checkmark-kick {
  position: absolute;
  width: 4px;
  height: 12px;
  background-color: #10b981;
  left: 15px;
  top: 23px;
  transform: rotate(-45deg);
  border-radius: 2px;
  animation: checkmark-kick 0.2s ease-in-out 0.3s both;
}

@keyframes checkmark-circle {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes checkmark-stem {
  0% {
    height: 0;
  }
  100% {
    height: 20px;
  }
}

@keyframes checkmark-kick {
  0% {
    width: 0;
  }
  100% {
    width: 4px;
  }
}

/* Error Icon */
.error-icon {
  width: 3rem;
  height: 3rem;
  border: 3px solid #ef4444;
  border-radius: 50%;
  position: relative;
  animation: error-shake 0.5s ease-in-out;
}

.error-line1,
.error-line2 {
  position: absolute;
  width: 3px;
  height: 16px;
  background-color: #ef4444;
  left: 50%;
  top: 50%;
  transform-origin: center;
}

.error-line1 {
  transform: translate(-50%, -50%) rotate(45deg);
  animation: error-line 0.3s ease-in-out 0.2s both;
}

.error-line2 {
  transform: translate(-50%, -50%) rotate(-45deg);
  animation: error-line 0.3s ease-in-out 0.3s both;
}

@keyframes error-shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

@keyframes error-line {
  0% {
    height: 0;
    opacity: 0;
  }
  100% {
    height: 16px;
    opacity: 1;
  }
}

/* Title Section */
.title-section {
  margin-bottom: 1.5rem;
}

.verify-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.verify-card.verifying .verify-title {
  color: #3b82f6;
}

.verify-card.success .verify-title {
  color: #10b981;
}

.verify-card.error .verify-title {
  color: #ef4444;
}

.title-underline {
  height: 3px;
  width: 4rem;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
  margin: 0 auto;
  animation: underline-expand 0.5s ease-out;
}

@keyframes underline-expand {
  0% {
    width: 0;
  }
  100% {
    width: 4rem;
  }
}

/* Message */
.verify-message {
  font-size: 1rem;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

/* Progress Bar */
.progress-container {
  margin-bottom: 1.5rem;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 60%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 3px;
  animation: progress-pulse 2s ease-in-out infinite;
}

@keyframes progress-pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: translateX(-10px);
  }
  50% {
    opacity: 1;
    transform: translateX(10px);
  }
}

/* Status Boxes */
.status-box {
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  line-height: 1.4;
}

.success-box {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.error-box {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #991b1b;
}

/* Error Section */
.error-section {
  width: 100%;
}

/* Retry Button */
.retry-button {
  width: 100%;
  padding: 0.875rem 1.5rem;
  background: linear-gradient(135deg, #475569, #334155);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.retry-button:hover {
  background: linear-gradient(135deg, #334155, #1e293b);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.retry-button:active {
  transform: translateY(0);
}

.retry-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.retry-button:hover::before {
  left: 100%;
}

/* Footer */
.verify-footer {
  text-align: center;
  margin-top: 2rem;
}

.verify-footer p {
  font-size: 0.875rem;
  color: #64748b;
}

.support-link {
  color: #3b82f6;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.support-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 640px) {
  .verify-page {
    padding: 0.5rem;
  }

  .verify-content {
    padding: 2rem 1.5rem;
  }

  .verify-title {
    font-size: 1.5rem;
  }

  .icon-container {
    width: 3.5rem;
    height: 3.5rem;
  }

  .spinner {
    width: 2.5rem;
    height: 2.5rem;
  }

  .checkmark {
    width: 2.5rem;
    height: 2.5rem;
  }

  .error-icon {
    width: 2.5rem;
    height: 2.5rem;
  }
}

@media (max-width: 480px) {
  .verify-content {
    padding: 1.5rem 1rem;
  }

  .verify-title {
    font-size: 1.25rem;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.retry-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.support-link:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .verify-card {
    border: 2px solid #000;
    background: #fff;
  }

  .verify-title {
    color: #000;
  }

  .verify-message {
    color: #000;
  }
}
