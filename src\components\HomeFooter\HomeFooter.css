.footer {
  color: var(--text-dark);
  font-size: clamp(12px, 2vw, 14px);
  animation: fadeIn 1.5s ease-in;
  background-color: #fff;
  width: 100%;
  box-sizing: border-box;
}

.footer p {
  margin: 0;
  padding: 0;
}

.list-show-bar {
  padding: 60px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  line-height: 150%;
  width: 100%;
  box-sizing: border-box;
}
.list-show-content {
  position: relative;
  background: linear-gradient(180deg, #fcebedbf 0%, #ffffffbf 100%);
  padding: 40px 50px;
  width: 100%;
  max-width: calc(100% - 60px);
  margin: auto;
  border-radius: 25px;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  box-sizing: border-box;
}

.list-show-content::before {
  display: none;
}

.list-show-content h2 {
  font-size: 32px;
  font-weight: 600;
  color: var(--secondary-color);
  line-height: 140%;
  text-align: center;
  margin: 0;
}

.list-show-content p {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-light);
  line-height: 140%;
  margin: 0 0 20px 0;
  text-align: center;
}
.list-icon {
  width: 24px;
  height: 24px;
}

.list-show-content a {
  text-decoration: none;
}
.list-show-btn {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  cursor: pointer;
  line-height: 150%;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: fit-content;
  margin: 0 auto;
  transition: background-color 0.3s ease;
}

.list-show-btn:hover {
  background-color: var(--primary-color);
}

.list-icon {
  font-size: 22px;
  color: white;
}

.trust-section {
  text-align: center;
  padding: 50px 25px;
  background: linear-gradient(180deg, #fcebedbf 0%, #ffffffbf 100%);
  border-bottom: 2px solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}

.trust-section h2 {
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 35px;
  color: #111;
}

.trust-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

.trust-item {
  padding: 20px;
  width: 240px;
  background-color: #fff;
  text-align: center;
  border-radius: 15px;
  margin: 0 auto;
  box-sizing: border-box;
}

.trust-icon {
  font-size: 32px;
  color: var(--primary-color);
  margin-bottom: 12px;
}

.trust-item h3 {
  font-size: 15px;
  font-weight: 700;
  margin-bottom: 5px;
  color: var(--text-color);
}

.trust-item p {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-light);
  margin: 0;
}

.footer-container {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 30px;
}

.footer-logo {
  width: 160px;
  height: auto;
  flex-shrink: 0;
}

.footer-logo img {
  width: 100%;
  height: auto;
  object-fit: contain;
}
.footer-column-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 50px 24px;
  border-bottom: 1px solid #e0e0e0;
  gap: 20px;
  box-sizing: border-box;
}

.footer-column {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.footer-column ul {
  list-style: none;
  padding: 0;
  display: flex;
  gap: 24px;
  flex-direction: row;
  align-items: center;
  margin: 0;
  flex-wrap: wrap;
  justify-content: center;
}

.footer-column ul li {
  font-size: 16px;
  cursor: pointer;
  line-height: 150%;
  white-space: nowrap;
}

.footer-column ul li a {
  color: var(--text-light);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-column ul li:hover a {
  color: var(--primary-color);
  text-decoration: underline;
}

.app-links img {
  width: 120px;
  margin: 8px 8px 16px 0;
  cursor: pointer;
}

.social-icons {
  display: flex;
  gap: 10px;
  font-size: 18px;
  margin-top: 8px;
}

.social-icons svg {
  color: #ffffff;
  transition: color 0.3s;
}

.social-icons svg:hover {
  color: #fc1d00;
}

.footer-note {
  width: 100%;
  text-align: center;
  margin: clamp(15px, 3vw, 20px) 0;
  font-size: clamp(12px, 2vw, 14px);
  color: var(--text-light);
  padding: 0 20px;
  box-sizing: border-box;
}

/* Large Desktop (≥1440px) */
@media (min-width: 1440px) {
  .list-show-bar {
    padding: 80px 40px;
  }

  .list-show-content {
    padding: 50px 60px;
  }

  .trust-section {
    padding: 55px 30px;
  }

  .trust-grid {
    gap: 40px;
  }

  .footer-column-container {
    padding: 0 60px 30px;
  }
}

/* Desktop (1200px - 1439px) */
@media (max-width: 1439px) and (min-width: 1200px) {
  .list-show-bar {
    padding: 70px 30px;
  }

  .list-show-content {
    padding: 45px 50px;
  }

  .trust-section {
    padding: 50px 25px;
  }

  .trust-grid {
    gap: 35px;
  }

  .footer-column-container {
    padding: 0 50px 25px;
  }
}

/* Laptop (1024px - 1199px) */
@media (max-width: 1199px) and (min-width: 1024px) {
  .list-show-bar {
    padding: 60px 30px;
  }

  .list-show-content {
    padding: 40px 45px;
  }

  .trust-section {
    padding: 50px 25px;
  }

  .trust-grid {
    gap: 30px;
  }

  .trust-item {
    width: 220px;
  }

  .footer-column-container {
    padding: 0 40px 24px;
  }

  .footer-column ul {
    gap: 18px;
  }
}

/* Tablet Landscape (768px - 1023px) */
@media (max-width: 1023px) and (min-width: 768px) {
  .list-show-bar {
    padding: 50px 30px;
  }

  .list-show-content {
    max-width: calc(100% - 50px);
    padding: 40px 50px;
    border-radius: 30px;
  }

  .trust-section {
    padding: 45px 20px;
  }

  .trust-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 35px;
  }

  .trust-item {
    max-width: 280px;
  }

  .footer-column-container {
    padding: 0 30px 20px;
    gap: 15px;
  }

  .footer-column ul {
    gap: 18px;
  }
}

/* Tablet Portrait (600px - 767px) */
@media (max-width: 767px) and (min-width: 600px) {
  .list-show-bar {
    padding: 40px 25px;
  }

  .list-show-content {
    max-width: calc(100% - 40px);
    padding: 35px 40px;
    border-radius: 25px;
  }

  .trust-section {
    padding: 40px 18px;
  }

  .trust-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .trust-item {
    max-width: 260px;
    padding: 20px 15px;
  }

  .footer-column-container {
    flex-direction: column;
    gap: 20px;
    align-items: center;
    text-align: center;
    padding: 25px 25px 18px;
  }

  .footer-column ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }
}

/* Mobile Large (480px - 599px) */
@media (max-width: 599px) and (min-width: 480px) {
  .list-show-bar {
    padding: 35px 20px;
  }

  .list-show-content {
    max-width: calc(100% - 25px);
    padding: 30px 25px;
    border-radius: 20px;
  }

  .trust-section {
    padding: 35px 15px;
  }

  .trust-grid {
    grid-template-columns: 1fr;
    gap: 25px;
  }

  .trust-item {
    max-width: 320px;
    padding: 25px 20px;
  }

  .footer-column-container {
    flex-direction: column;
    gap: 18px;
    padding: 20px 20px 15px;
  }

  .footer-logo {
    width: 140px;
  }

  .footer-column ul {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

/* Mobile Medium (375px - 479px) */
@media (max-width: 479px) and (min-width: 375px) {
  .list-show-bar {
    padding: 30px 15px;
  }

  .list-show-content {
    max-width: calc(100% - 10px);
    padding: 25px 20px;
    gap: 15px;
  }

  .trust-section {
    padding: 30px 12px;
  }

  .trust-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .trust-item {
    padding: 20px 15px;
  }

  .footer-column-container {
    flex-direction: column;
    gap: 15px;
    padding: 18px 15px 12px;
  }

  .footer-logo {
    width: 120px;
  }

  .footer-column ul {
    flex-direction: column;
    gap: 10px;
  }

  .footer-column ul li {
    font-size: 13px;
  }

  .footer-container {
    margin: 0;
  }
}

/* Mobile Small (≤374px) */
@media (max-width: 374px) {
  .list-show-bar {
    padding: 25px 12px;
  }

  .list-show-content {
    max-width: calc(100% - 10px);
    padding: 20px 15px;
    gap: 12px;
    border-radius: 15px;
  }

  .trust-section {
    padding: 25px 10px;
  }

  .trust-grid {
    gap: 18px;
  }

  .trust-item {
    padding: 18px 12px;
  }

  .trust-icon {
    font-size: 24px;
    margin-bottom: 10px;
  }
  .footer-container {
    margin: 0;
  }
  .footer-column-container {
    flex-direction: column;
    gap: 12px;
    padding: 15px 12px 10px;
  }

  .footer-logo {
    width: 100px;
  }

  .footer-column ul li {
    font-size: 12px;
  }

  .footer-note {
    font-size: 10px;
    margin: 12px 0;
  }
}
