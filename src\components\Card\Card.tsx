import "./Card.css";
import React from "react";
import { Link } from "react-router-dom";

type CardProps = {
  imageUrl: string;
  title: string;
  slug: string;
  description: string;
};

const Card: React.FC<CardProps> = ({ imageUrl, title, slug, description }) => {
  return (
    <Link to={`/event-details/${slug}`} className="card-link">
      <div className="card fade-in">
        <img src={imageUrl} alt={title} className="card-image" />
        <div className="card-body">
          <h3 className="card-title">{title}</h3>
          <p className="card-description card-clamped-description">
            {description}
          </p>
        </div>
      </div>
    </Link>
  );
};

export default Card;
