:root {
  /* Primary Colors */
  --primary-color: #e63a4a;
  --secondary-color: #e63a4a;
  --accent-secondary: #ff5e7a;
  --accent-tertiary: #b73555;

  /* Text Colors */
  --text-color: #333333;
  --text-light: #666666;
  --text-dark: #1a1a1a;

  /* Background Colors */
  --background-color: #fff;
  --background-dark: #e0e0e0;
  --light-background-color: #f7f7f7;
  --dark-mode-bg: #121212;
  --highlight-background: #fff3f4;

  /* Button Colors */
  --btn-text-color: #ffffff;
  --btn-primary: #e63a4a;
  --btn-secondary: #ff7d7d;
  --btn-danger: #d32f2f;
  --btn-success: #388e3c;
  --btn-info: #1976d2;
  --btn-warning: #f57c00;
  --text-muted: #888888;

  /* Border and Shadow */
  --border-color: #ddd;
  --border-dark: #999;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-dark: rgba(0, 0, 0, 0.2);

  /* Gradients */
  --gradient-primary: linear-gradient(45deg, #e63a4a, #b73555);
  --gradient-secondary: linear-gradient(45deg, #ff7d7d, #e63a4a);
  --gradient-accent: linear-gradient(45deg, #b73555, #ff5e7a);

  /* Special Colors */
  --overlay-color: rgba(0, 0, 0, 0.5);
  --muted-color: hsl(0, 0%, 70%);
  --highlight-color: #ffeb3b;

  --main-border-radius: 5px;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  user-select: none;
}

* {
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: "Inter", sans-serif;
  margin: 0;
  padding: 0;
  background-color: white;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  line-height: 150%;
  margin: 0px;
}
