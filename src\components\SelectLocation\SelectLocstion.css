.step-container {
  margin-top: 15px;
}

.location-card-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.location-card {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  border: 2px solid #ccc;
  border-radius: 12px;
  cursor: pointer;
  background-color: #fff;
  gap: 10px;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  user-select: none;
}

.location-card:hover {
  border-color: var(--accent-secondary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.location-card.selected {
  border-color: var(--primary-color);
  background-color: #ffefef;
}

.radio-column {
  display: flex;
  align-items: center;
  margin-top: 2px;
  margin-left: -5px;
}

.radio-column input[type="radio"] {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  accent-color: #f66173;
  cursor: pointer;
}

.info-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
  justify-content: center;
}

.venue-name {
  font-weight: 700;
  font-size: 18px;
  line-height: 150%;
}

.venue-address {
  color: #555;
  font-size: 15px;
  line-height: 150%;
  text-align: left;
}
