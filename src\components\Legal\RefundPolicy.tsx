const RefundPolicyPage = () => {
  return (
    <main className="legal-page">
      <header className="legal-header">
        <h1>Refund Policy – Pravesh</h1>
        <p className="date">Effective Date: July 08, 2025</p>
      </header>

      <div className="legal-content">
        <section className="legal-intro">
          <p>
            We understand that refund clarity is important. <PERSON><PERSON>sh ensures that
            all refund processes are transparent and based on the event
            organizer’s refund terms.
          </p>
        </section>

        <section>
          <h2>1. Refund Eligibility</h2>
          <ul>
            <li>The event is canceled by the organizer.</li>
            <li>
              The event is rescheduled and the user opts out within the
              specified window.
            </li>
            <li>
              Cancellation is permitted by the organizer and done within the
              allowed period.
            </li>
            <li>
              Duplicate payment or technical issues occurred during booking.
            </li>
          </ul>
        </section>

        <section>
          <h2>2. Refund Amount</h2>
          <ul>
            <li>
              <strong>Full Refund:</strong> Issued if the event is canceled by
              the organizer or technical failure results in double payment.
            </li>
            <li>
              <strong>Partial Refund:</strong> Applicable where organizers
              deduct a cancellation fee or if only a portion of the booking is
              eligible.
            </li>
            <li>
              <strong>Non-Refundable Charges:</strong> Platform fees or
              convenience charges may not be refunded unless the fault lies with
              Pravesh.
            </li>
          </ul>
        </section>

        <section>
          <h2>3. Refund Timeline</h2>
          <ul>
            <li>
              Refunds will be processed within 7–14 working days to the original
              payment method (e.g., UPI, credit/debit card, wallet).
            </li>
            <li>
              Delays due to banks or third-party gateways are beyond our
              control.
            </li>
          </ul>
        </section>

        <section>
          <h2>4. No Refund Scenarios</h2>
          <ul>
            <li>No-shows (not attending the event without cancellation).</li>
            <li>Cancellation requests made after the allowed period.</li>
            <li>Events marked as non-refundable.</li>
          </ul>
        </section>

        <section>
          <h2>5. How to Request a Refund</h2>
          <ul>
            <li>
              To request a refund, send an email to{" "}
              <a href="mailto:<EMAIL>"><EMAIL></a> with:
              <ul style={{ listStyleType: "circle", paddingLeft: "1.5rem" }}>
                <li>Booking ID</li>
                <li>Reason for refund</li>
                <li>Payment proof (if needed)</li>
              </ul>
            </li>
          </ul>
        </section>
      </div>
    </main>
  );
};

export default RefundPolicyPage;
