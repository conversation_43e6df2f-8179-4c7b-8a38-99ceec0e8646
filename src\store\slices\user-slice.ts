import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import { API_URL } from "../../utils/env";
import { UserState } from "../../Interfaces/user-store.interface";
import { User } from "../../Interfaces/user.interface";
import { jwtDecode } from "jwt-decode";
import axiosInstance from "../../services/axiosInstance";

const initialState: UserState = {
  user: null,
  role: null,
  accessToken: null,
  isLogging: false,
  loading: false,
  error: null,
};

export const verifyOTP = createAsyncThunk<
  { user: User; token: string },
  { phone: string; otp: string },
  { rejectValue: string }
>("user/verifyOTP", async (authdata, thunkAPI) => {
  try {
    const { data } = await axiosInstance.post(`/auth/verifyOTP`, authdata);
    return data;
  } catch (error: any) {
    return thunkAPI.rejectWithValue(
      error?.response?.data?.message || "Login failed"
    );
  }
});

export const fetchUser = createAsyncThunk<
  { data: User; token: string },
  void,
  { rejectValue: string }
>("user/fetchUser", async (_, thunkAPI) => {
  try {
    const token = localStorage.getItem("auth_token");
    if (!token) throw new Error("Token not found");

    const decodedToken = jwtDecode(token) as { role: string } | null;
    if (!decodedToken) {
      throw new Error("Unauthorized role");
    }

    const res = await axios.get(`${API_URL}/users/profile`, {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    return { data: res.data, token };
  } catch (error: any) {
    return thunkAPI.rejectWithValue(
      error?.response?.data?.message || "Fetch user failed"
    );
  }
});

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUserState: (state) => {
      const userData = localStorage.getItem("user");
      const token = localStorage.getItem("token");
      state.user = userData ? JSON.parse(userData) : null;
      state.accessToken = token || null;
    },
    logout: (state) => {
      localStorage.clear();
      state.user = null;
      state.accessToken = null;
    },
    setProfileName: (state, action) => {
      state.user = {
        ...state.user,
        ...action.payload,
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(verifyOTP.pending, (state) => {
        state.loading = false;
      })
      .addCase(verifyOTP.fulfilled, (state, action) => {
        state.user = action.payload.user;
        state.accessToken = action.payload.token;
        state.loading = false;
        state.isLogging = true;
        state.error = null;
        localStorage.setItem("auth_token", state.accessToken);
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.isLogging = false;
        state.loading = false;
        state.error = action.payload || action.error.message || "Login failed";
        localStorage.removeItem("auth_token");
      })
      .addCase(fetchUser.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchUser.fulfilled, (state, action) => {
        state.user = action.payload.data;
        state.accessToken = action.payload.token;
        state.role = action.payload.data.role;
        state.loading = false;
      })
      .addCase(fetchUser.rejected, (state) => {
        state.user = null;
        state.loading = false;
        state.accessToken = null;
        localStorage.clear();
      });
  },
});

export const { setUserState, logout, setProfileName } = userSlice.actions;

export default userSlice.reducer;
