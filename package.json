{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.6", "@reduxjs/toolkit": "^2.7.0", "axios": "^1.8.4", "framer-motion": "^12.5.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lottie-react": "^2.4.1", "moment": "^2.30.1", "razorpay": "^2.9.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.3.0", "swiper": "^11.2.10"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.9", "@types/leaflet": "^1.9.20", "@types/node": "^22.15.3", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "i": "^0.3.7", "npm": "^11.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}