.ticket-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 50px;
  color: var(--primary-color) !important;
  background-color: white;
  border-bottom: 2px solid var(--primary-color);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: auto;
  z-index: 999;
  height: 70px;
}

.ticket-header h4 {
  font-size: 30px;
}

.booking-container {
  max-width: 600px;
  display: flex;
  flex-direction: column;
  min-height: 500px;
  border-radius: 12px;
  padding: 1rem 2rem;
  margin: auto;
  margin-top: 90px;
  padding-bottom: 0;
}

.booking-container .event-info {
  color: #000;
}
.breadcrumb {
  display: flex;
  gap: 3rem;
  font-weight: 600;
  font-size: 0.95rem;
  color: #777;
  margin-bottom: 25px;
  user-select: none;
  justify-content: space-between;
}

.breadcrumb .step-label {
  font-size: 16px;
  line-height: 150%;
  position: relative;
  border-radius: 20px;
  white-space: nowrap;
  transition: background-color 0.3s, color 0.3s;
}

.breadcrumb .step-label.active {
  color: var(--primary-color);
  font-weight: 700;
}

.breadcrumb span:hover:not(.active) {
  color: #000;
  cursor: pointer;
}

.step {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.line {
  padding: 2px;
  height: 1px;
  border-radius: 5px;
  width: 100%;
  background-color: #555;
}

.line.active {
  background-color: var(--primary-color);
}

.ticket-main-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.btn-container {
  display: flex;
  align-items: center;
  gap: 20px;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.btn-container button {
  font-size: 15px;
  font-weight: 400;
  padding: 5px 10px;
  cursor: pointer;
  border-color: transparent;
  border-radius: 5px;
}

.next-btn {
  color: #fff;
  background-color: var(--primary-color);
}

.step-container {
  overflow-y: auto;
  max-height: calc(100vh - 275px);
  text-align: center;
  margin-bottom: 30px;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.step-container::-webkit-scrollbar {
  display: none;
}

.location-container {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  gap: 8px;
  border-radius: 10px;
  border: 1px solid #ccc;
  padding: 16px 24px;
  flex-direction: column;
  background-color: var(--light-background-color);
  margin-bottom: 15px;
}

.location-header {
  display: flex;
  gap: 4px;
  align-items: flex-start;
  flex-direction: column;
  margin-bottom: 10px;
}

.location-title {
  font-size: 16px;
  line-height: 150%;
  font-weight: 700;
  color: #111;
}
.location-address {
  font-size: 14px;
  font-weight: 400;
  color: #555;
}
.ticket-type {
  font-size: 1.1rem;
  margin-bottom: 0.3rem;
}
.ticket-group {
  margin-bottom: 30px;
}

.ticket-group:last-child {
  margin-bottom: 0 !important;
}
.ticket-price {
  font-size: 0.95rem;
  margin-bottom: 0.2rem;
  color: #333;
}

.ticket-qty {
  font-size: 0.85rem;
  color: #555;
}

.next-btn a {
  text-decoration: none;
  color: #fff;
}

@media only screen and (max-width: 600px) {
  .step-container {
    height: 100%;
    overflow-y: unset;
  }

  .ticket-main-content {
    gap: 30px;
    padding-bottom: 20px;
  }

  .ticket-header {
    padding: 20px 20px;
  }
}

@media (min-width: 600px) {
  .card {
    width: 200px;
  }
}
