/* Layout */
.login-wrapper {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

/* Left side (form) */
.login-left {
  width: 50%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  animation: fadeInLeft 1s ease;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
}

/* Glow background */
.login-left .glow {
  position: absolute;
  width: 150px;
  height: 150px;
  top: 20%;
  left: 10%;
  background: radial-gradient(circle, rgba(255, 0, 75, 0.08), transparent 70%);
  filter: blur(50px);
  z-index: 1;
  animation: pulse 5s ease-in-out infinite;
}

/* Form Styling */
.login-form {
  margin: 30px;
  width: 100%;
  max-width: 400px;
  animation: fadeInLeft 1.2s ease;
  z-index: 2;
}

.login-form input {
  background-color: #f1f1f1 !important;
  border: 1px solid #ddd;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 15px;
}

input[type="checkbox"] {
  accent-color: var(--primary-color);
  width: 14px;
  height: 14px;
  margin: 0;
  margin-bottom: -1px;
}

/* Title */
.login-title {
  text-align: center;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-weight: 600;
  font-size: 1.75rem;
}

/* Button */
.login-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem;
  font-size: 16px;
  font-weight: 700;
  border-radius: 8px;
  cursor: pointer;
  width: 100%;
  transition: background-color 0.3s ease, transform 0.2s;
  margin-top: 10px;
}

.login-btn:hover {
  background-color: #c42e3e;
  transform: translateY(-1px);
}

.login-btn.disabled {
  background-color: rgba(230, 58, 74, 0.5);
  cursor: not-allowed;
}

/* Right side (branding) */
.login-right {
  width: 50%;
  background-color: var(--primary-color);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  animation: fadeInRight 1s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

/* Right content */
.login-overlay-content {
  padding: 5rem 2.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  animation: fadeInRight 1.2s ease;
  z-index: 2;
}

.login-overlay-content img {
  width: 100px;
  height: 100px;
}

.login-overlay-subtext {
  font-size: 16px;
  max-width: 300px;
  opacity: 0.85;
}

.error-text {
  margin-top: 5px;
}
/* Responsive */
@media (max-width: 768px) {
  .login-wrapper {
    flex-direction: column-reverse;
  }

  .login-left,
  .login-right {
    width: 100%;
    height: 50%;
  }

  .login-overlay-content {
    padding: 3rem 1.5rem;
    gap: 15px;
  }
  .login-overlay-content img {
    width: 70px;
    height: 70px;
  }

  .login-overlay-title {
    font-size: 20px;
  }

  .modal-content {
    margin: 0 20px;
  }
}
.phone-input-group {
  display: flex;
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f1f1f1;
}

.phone-input-group .input-wrapper {
  margin-bottom: 0;
}

.phone-input-group .phone-input {
  flex: 1;
  border: none;
  background-color: transparent;
  padding: 0.75rem 1rem;
  font-size: 15px;
  outline: none;
}
.country-select {
  border: none;
  padding: 0 0.75rem;
  font-size: 15px;
  background-color: #f1f1f1;
  color: #333;
  outline: none;
  border-right: 1px solid #ccc;
  appearance: none;
  width: 70px;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg fill='black' viewBox='0 0 24 24' width='16' height='16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 12px;
}

.terms-checkbox {
  margin: 12px 0;
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.terms-checkbox label {
  cursor: pointer;
  margin-top: -2px;
}

.terms-checkbox a {
  color: #007bff;
  text-decoration: underline;
}

.otp-section {
  margin-top: 20px;
  text-align: center;
}

.otp-instruction {
  font-size: 14px;
  margin-bottom: 10px;
}

.otp-inputs {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.otp-box {
  width: 40px;
  height: 40px;
  padding: 0 !important;
  text-align: center;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.profile-title {
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  font-weight: 600;
  font-size: 1.5rem;
}

.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid #ccc;
  border-top: 2px solid #e63a4a;
  border-radius: 50%;
  animation: spin 0.7s linear infinite;
}

/* Fullscreen loader overlay */
.global-loader-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Spinner inside overlay */
.global-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #ccc;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.otp-loader .spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #fff;
  border-top: 3px solid #e63a4a;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
  margin: auto;
}

/* Hide spinner for Chrome, Safari, Edge */
.otp-box::-webkit-outer-spin-button,
.otp-box::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Hide spinner for Firefox */
.otp-box {
  -moz-appearance: textfield;
}
