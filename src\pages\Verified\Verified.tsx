import type React from "react";
import { useEffect, useState } from "react";
import "./Verified.css";
import { useSearchParams, useNavigate } from "react-router-dom";
import { RoutePaths } from "../../utils/route.enm";

const VerifyPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const [status, setStatus] = useState<"verifying" | "success" | "error">(
    "verifying"
  );
  const [message, setMessage] = useState<string>("Verifying your email...");

  useEffect(() => {
    const error = searchParams.get("error");
    const userId = searchParams.get("user");

    if (error) {
      setStatus("error");
      setMessage(decodeURIComponent(error));
    } else if (userId) {
      setStatus("success");
      setMessage("Your email has been verified successfully!");
      setTimeout(() => {
        navigate(RoutePaths?.Home || "/");
      }, 2500);
    }
  }, [searchParams, navigate]);

  const getStatusIcon = () => {
    switch (status) {
      case "verifying":
        return (
          <div className="icon-container">
            <div className="spinner"></div>
          </div>
        );
      case "success":
        return (
          <div className="icon-container success">
            <div className="checkmark">
              <div className="checkmark-circle"></div>
              <div className="checkmark-stem"></div>
              <div className="checkmark-kick"></div>
            </div>
          </div>
        );
      case "error":
        return (
          <div className="icon-container error">
            <div className="error-icon">
              <div className="error-line1"></div>
              <div className="error-line2"></div>
            </div>
          </div>
        );
    }
  };

  const getStatusTitle = () => {
    switch (status) {
      case "verifying":
        return "Verifying Your Email";
      case "success":
        return "Email Verified Successfully!";
      case "error":
        return "Verification Failed";
    }
  };

  return (
    <div className="verify-page">
      <div className="verify-container">
        <div className={`verify-card ${status}`}>
          <div className="verify-content">
            {/* Status Icon */}
            <div className="icon-section">{getStatusIcon()}</div>

            {/* Title */}
            <div className="title-section">
              <h1 className="verify-title">{getStatusTitle()}</h1>
              <div className="title-underline"></div>
            </div>

            {/* Message */}
            <p className="verify-message">{message}</p>

            {/* Progress Bar */}
            {status === "verifying" && (
              <div className="progress-container">
                <div className="progress-bar">
                  <div className="progress-fill"></div>
                </div>
              </div>
            )}

            {/* Success Info */}
            {status === "success" && (
              <div className="status-box success-box">
                <p>Redirecting you to the homepage in a few seconds...</p>
              </div>
            )}

            {/* Error Retry */}
            {status === "error" && (
              <div className="error-section">
                <div className="status-box error-box">
                  <p>
                    Please try again or contact support if the problem persists.
                  </p>
                </div>
                <button onClick={() => navigate("/")} className="retry-button">
                  Return to Homepage
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="verify-footer">
          <p>
            Need help? Contact our{" "}
            <a href="/support" className="support-link">
              support team
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default VerifyPage;
