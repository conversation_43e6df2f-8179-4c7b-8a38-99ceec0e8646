.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color, white);
  z-index: 9999;
}

.loader-text {
  font-size: 28px;
  font-weight: bold;
  margin-top: -80px;
  font-size: 34px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    #fff,
    var(--primary-color)
  );
  background-size: 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer 2s linear infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% center;
  }
  100% {
    background-position: -200% center;
  }
}
