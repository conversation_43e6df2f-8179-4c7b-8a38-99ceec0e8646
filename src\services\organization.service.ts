import axios from "axios";
import { IEvent } from "../Interfaces/organization.interface";
import { API_URL } from "../utils/env";

export const getEventData = async (slug: string): Promise<IEvent> => {
  try {
    const res = await axios.get(`${API_URL}/organization/get-event/${slug}`);
    return res.data;
  } catch (error: any) {
    console.error("Error fetching carousal data:", error);
    throw new Error(
      error?.response?.data?.message || "Failed to fetch carousal data"
    );
  }
};
