import "./AssignTicket.css";
import moment from "moment";
import axiosInstance from "../../services/axiosInstance";
import * as Dialog from "@radix-ui/react-dialog";
import Header from "../../components/Header/Header";
import React, { useEffect, useState } from "react";
import { FaQrcode, FaTicketAlt } from "react-icons/fa";
import { useAppSelector } from "../../store/hooks";
import { IEvent } from "../../Interfaces/organization.interface";
import { IAssignTicket } from "../../Interfaces/user.interface";
import Loader from "../../components/Loader/Loader";

interface GroupedTickets {
  [eventId: string]: {
    event: IEvent;
    venues: {
      [venueId: string]: IAssignTicket[];
    };
  };
}

const AssignTicket: React.FC = () => {
  const [tickets, setTickets] = useState<GroupedTickets>({});
  const [qrDialogOpen, setQrDialogOpen] = useState(false);
  const { user } = useAppSelector((state) => state.user);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    fetchTickets();
  }, []);

  const fetchTickets = async () => {
    try {
      const response = await axiosInstance.get("/users/tickets");
      const confirmedTickets: IAssignTicket[] = response.data;

      const sortedTickets = [...confirmedTickets].sort(
        (a, b) =>
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );

      const grouped: GroupedTickets = {};

      for (const ticket of sortedTickets) {
        const event = ticket.event as IEvent;
        const venueId = ticket.venue._id;

        if (!grouped[event._id]) {
          grouped[event._id] = {
            event,
            venues: {},
          };
        }

        if (!grouped[event._id].venues[venueId]) {
          grouped[event._id].venues[venueId] = [];
        }

        grouped[event._id].venues[venueId].push(ticket);
      }

      setTickets(grouped);
    } catch (error) {
      console.error("Error fetching ticket data:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <Loader />;
  }

  return (
    <>
      <Header />
      <div className="assign-container">
        <div className="assign-header">
          <h1 className="assign-title">
            <FaTicketAlt />
            Your Assigned Tickets
          </h1>
          <div className="assign-icons">
            <FaQrcode
              className="assign-qr-icon"
              onClick={() => setQrDialogOpen(true)}
            />
          </div>
        </div>

        <div className="assign-ticket-section">
          <div className="assign-ticket-list">
            {Object.keys(tickets).length > 0 ? (
              Object.entries(tickets).map(([eventId, eventData]) => (
                <div key={eventId} className="assign-event-block">
                  <h2 className="assign-event-title">{eventData.event.name}</h2>
                  <p className="assign-event-date">
                    📅 {moment(eventData.event.startDate).format("DD MMM YYYY")}
                  </p>

                  {Object.entries(eventData.venues).map(
                    ([venueId, venueTickets]) => {
                      const venueName =
                        venueTickets[0]?.venue?.venue || "Unknown Venue";
                      return (
                        <div key={venueId} className="assign-venue-block">
                          <h3 className="assign-venue-title">
                            🏟️ Venue: {venueName}
                          </h3>

                          {venueTickets.map((ticket) => (
                            <div
                              key={ticket._id}
                              className="assign-ticket-card"
                            >
                              <div className="assign-ticket-header">
                                <h4>{ticket.ticketTypeDetails.type} Ticket</h4>
                                <p
                                  className={`assign-ticket-status status-${ticket.status.toLowerCase()}`}
                                >
                                  {ticket.status}
                                </p>
                              </div>
                              <p>🎫 Quantity: {ticket.quantity}</p>
                              {!!ticket.price && (
                                <p>💵 Price: ₹{ticket.price}</p>
                              )}
                              <p>
                                🕓 Booked On:{" "}
                                {moment(ticket.createdAt).format("DD MMM YYYY")}
                              </p>
                            </div>
                          ))}
                        </div>
                      );
                    }
                  )}
                </div>
              ))
            ) : (
              <p>No confirmed tickets available.</p>
            )}
          </div>
        </div>

        {/* QR Dialog */}
        <Dialog.Root open={qrDialogOpen} onOpenChange={setQrDialogOpen}>
          <Dialog.Portal>
            <Dialog.Overlay className="assign-dialog-overlay" />
            <Dialog.Content className="assign-dialog-content">
              <Dialog.Title className="assign-qr-title">
                Your QR Code
              </Dialog.Title>

              <Dialog.Description className="assign-qr-description">
                Scan this QR code to verify your ticket at the event.
              </Dialog.Description>

              <img
                src={`${user?.qrCode}`}
                alt="QR Code"
                className="assign-qr-image"
              />
            </Dialog.Content>
          </Dialog.Portal>
        </Dialog.Root>
      </div>
    </>
  );
};

export default AssignTicket;
