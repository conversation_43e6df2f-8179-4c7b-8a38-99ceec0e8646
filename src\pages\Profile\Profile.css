.profile-container {
  min-height: 100vh;
  background: linear-gradient(to bottom right, #f8fafc, #f1f5f9);
  padding: 2rem;
  margin-top: 94px;
}

.profile-wrapper {
  max-width: 1024px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-header {
  text-align: center;
}

.profile-header h1 {
  font-size: 2rem;
  font-weight: bold;
  color: #111827;
}

.profile-header p {
  color: #4b5563;
}

.profile-card {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e5e7eb;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.05);
}

.profile-card-header {
  /* background: linear-gradient(to right, #2563eb, #7c3aed); */

  background: var(--gradient-primary);
  color: white;
  padding: 1.5rem;
  padding-bottom: 5rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.profile-info-text h2 {
  font-size: 1.25rem;
  font-weight: 600;
}

.profile-info-text p {
  color: #dbeafe;
}

.edit-btn,
.cancel-btn,
.save-btn {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  font-weight: 500;
  font-size: 16px;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  white-space: nowrap;
}

.edit-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.edit-btn:hover,
.cancel-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.save-btn {
  background: #2cc664;
  color: white;
}

.save-btn:hover {
  background: #22ba59;
}

.cancel-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.profile-card-content {
  padding: 1.5rem;
  padding-top: 0;
}

.profile-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -4rem;
  margin-bottom: 2rem;
}

.avatar-img {
  width: 8rem;
  height: 8rem;
  border-radius: 9999px;
  border: 4px solid white;
  background: var(--gradient-secondary);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-initials {
  font-size: 1.5rem;
  color: white;
  font-weight: bold;
}

.profile-basic-info {
  text-align: center;
  max-width: 16rem;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.profile-basic-info input {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  text-align: center;
}

.profile-basic-info h3 {
  font-size: 1.5rem;
  font-weight: bold;
  color: #111827;
}

.profile-basic-info p {
  color: #4b5563;
}

.active-status {
  background: #d1fae5;
  color: #065f46;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  margin-top: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.full-width {
  grid-column: span 2;
}

.profile-email-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
}

.form-group input,
.form-group select {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  line-height: 150%;
  letter-spacing: 0.5px;
  font-size: 16px;
  height: 40px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: inherit;
}

.profile-status {
  margin-top: 2rem;
  padding: 1rem;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 0.5rem;
  color: #1d4ed8;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 16px;
  font-weight: 600;
}

.status-text {
  font-size: 14px;
  margin-top: 10px;
}
svg {
  width: 16px;
  height: 16px;
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .form-group {
    grid-column: span 2;
  }
}
