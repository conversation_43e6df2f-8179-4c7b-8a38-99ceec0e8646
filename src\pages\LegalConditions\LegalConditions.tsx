import "./LegalConditions.css";
import { useLocation } from "react-router-dom";
import { useMemo } from "react";
import TermsAndConditionsPage from "../../components/Legal/TermsAndConditionsPage";
import PrivacyPolicyPage from "../../components/Legal/PrivacyPolicyPage";
import CancellationPolicyPage from "../../components/Legal/CancellationPolicy";
import RefundPolicyPage from "../../components/Legal/RefundPolicy";
import ShippingPolicyPage from "../../components/Legal/ShippingPolicyPage";
import { RoutePaths } from "../../utils/route.enm";
import ContactUsPage from "../../components/Legal/ContactUs";

const contentMap: Record<string, { content: any }> = {
  [RoutePaths.PrivacyPolicy]: {
    content: <PrivacyPolicyPage />,
  },
  [RoutePaths.TermsConditions]: {
    content: <TermsAndConditionsPage />,
  },
  [RoutePaths.RefundPolicy]: {
    content: <RefundPolicyPage />,
  },
  [RoutePaths.CancellationPolicy]: {
    content: <CancellationPolicyPage />,
  },
  [RoutePaths.ShippingDeliveryPolicy]: {
    content: <ShippingPolicyPage />,
  },
  [RoutePaths.ContactUs]: {
    content: <ContactUsPage />,
  },
};

const LegalConditions = () => {
  const location = useLocation();

  const pageData = useMemo(() => {
    const path = location.pathname;
    return contentMap[path] || { content: "Content not available." };
  }, [location.pathname]);

  const showDisclaimer = location.pathname !== RoutePaths.ContactUs;

  return (
    <div className="legal-page-container">
      <div className="text-base leading-relaxed whitespace-pre-line">
        {pageData.content}

        {showDisclaimer && (
          <div className="legal-disclaimer">
            <p>
              We reserve the right to update, modify, or revise this Privacy
              Policy and our Terms and Conditions at any time, at the sole
              discretion of the event organizer or owner. Such changes may be
              made to reflect updates in our practices, operational needs, legal
              or regulatory requirements, or to accommodate specific
              event-related considerations.
            </p>
            <br />
            <p>
              You are advised to review this page periodically for any changes.
              We will notify you of any material changes by updating the “Last
              Updated” date at the top of this policy. Continued use of the
              platform or services after such modifications constitutes your
              acceptance of the updated terms.
            </p>
            <br />
            <p>
              If you do not agree with the revised policy or terms, you may
              choose to discontinue use of our services.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default LegalConditions;
