.cart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 97px;
  width: 100%;
}

.cart-title {
  font-size: 28px;
  font-weight: bold;
  color: #d32f2f;
  text-transform: uppercase;
  margin-bottom: 20px;
  text-align: left;
  width: 90%;
}

.cart-content {
  display: flex;
  width: 90%;
  justify-content: space-between;
  gap: 30px;
  align-items: flex-start;
}

.cart-left {
  flex: 1.7;
  padding: 25px;
  border-radius: 20px;
  background: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid #e0e7ff;
  min-width: 0;
}

.cart-right.modern-summary {
  flex: 1;
  padding: 25px 30px;
  border-radius: 20px;
  background: linear-gradient(to bottom right, #fff0f3, #ffffff);
  border: 1px solid #ffe4e6;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 100px;
  height: fit-content;
}

.event-cart-title {
  font-size: 20px;
  font-weight: bold;
  color: black;
  margin-bottom: 15px;
  text-transform: uppercase;
  text-align: left;
}
.modern-summary h2 {
  margin-bottom: 15px;
}
.event-summary-card {
  background: #f9f9fb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1rem 1.2rem;
  margin-bottom: 1rem;
}

.event-summary-card h3 {
  font-size: 1.1rem;
  color: #111827;
}

.ticket-list {
  list-style: none;
  padding-left: 0;
  margin: 0 0 16px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 10px 14px;
}

.ticket-list li {
  display: flex;
  justify-content: space-between;
  padding: 6px 0;
  font-size: 0.95rem;
  color: #374151;
  border-bottom: 1px solid #f3f4f6;
}

.ticket-list li:last-child {
  border-bottom: none;
}

.ticket-list li span {
  width: 50%;
  word-wrap: break-word;
}

.ticket-list li span:nth-child(2) {
  text-align: right;
}

.ticket-cart-name {
  font-weight: 600;
  color: #1f2937;
}

.ticket-detail {
  color: #6b7280;
  font-size: 0.9rem;
}

.event-subtotal {
  text-align: right;
  font-weight: bold;
  color: #111827;
  margin-top: 0.5rem;
}

.summary-footer {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
  margin-top: 1.5rem;
}

.checkout-btn.modern {
  margin-top: 1rem;
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 5px;
  background-color: #e63a4a;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.checkout-btn.modern:hover:not(:disabled) {
  background-color: #c72e3d;
}

.checkout-btn.modern:disabled {
  background-color: rgba(230, 58, 74, 0.5);
  cursor: not-allowed;
}

.ticket-cart-date-heading {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.ticket-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  background-color: #f9f9f9;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.02);
  cursor: pointer;
  gap: 1rem;
  transition: all 0.3s ease;
  margin-bottom: 20px;
  margin-left: 10px;
}

.ticket-card:hover {
  background-color: #ffefef;
  transform: translateY(-2px);
  border-color: var(--border-color);
}

.ticket-card.selected {
  border-color: var(--primary-color);
  background-color: var(--selected-bg);
}

.ticket-info {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.ticket-control-wrap {
  display: flex;
  flex-direction: row;
  gap: 15px;
}
.ticket-name {
  font-weight: 700;
  font-size: 1.15rem;
  color: var(--text-dark);
  line-height: 150%;
}

.ticket-date {
  font-size: 0.9rem;
  font-weight: 600;
  color: #4b5563;
  background-color: #f3f4f6;
  padding: 6px 12px;
  margin: 10px 0;
  border-left: 4px solid #e63a4a;
  border-radius: 4px;
}

.ticket-detail-date {
  font-size: 1rem;
  font-weight: 600;
  color: #4b5563;
  line-height: 150%;
}
.ticket-price {
  /* color: #6b7280; */
  color: #e63a4a;
  font-weight: 500;
  font-size: 15px;
  text-align: left;
  margin-top: 5px;
}

.ticket-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.5rem;
}
.ticket-date-heading {
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: left;
}

.ticket-available {
  font-size: 14px;
  color: #ffffff;
  background-color: var(--primary-color);
  padding: 4px 10px;
  border-radius: 999px;
  font-weight: 500;
}

.ticket-counter {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 7px;
  padding: 4px 10px;
  font-weight: 600;
  border: 1px solid var(--border-dark);
  background-color: #fdfdfd;
}
.ticket-counter input {
  border: none !important;
}

.counter-btn {
  border: none;
  font-size: 20px;
  font-weight: 600;
  cursor: pointer;
  color: var(--primary-color);
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 25px;
  height: 25px;
  transition: background 0.2s ease;
  user-select: none;
}

.counter-btn:hover {
  background-color: #ffe4e6;
  border-radius: 6px;
}

.counter-input {
  width: 40px;
  height: 25px;
  text-align: center;
  font-size: 1rem;
  border: 1px solid var(--border-dark);
  border-radius: 0.5rem;
  padding: 0.25rem;
  background-color: #fff;
  color: var(--text-dark);
  padding: 0 !important;
  margin-bottom: -19px !important;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

@media (max-width: 768px) {
  .cart-content {
    flex-direction: column !important;
  }

  .cart-left,
  .cart-right.modern-summary {
    width: 100%;
  }
}

.cart-left,
.cart-right.modern-summary {
  transition: all 0.3s ease;
}

.cart-container {
  padding-bottom: 60px;
}
