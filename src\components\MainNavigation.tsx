import { Route, Routes } from "react-router-dom";
import { RoutePaths } from "../utils/route.enm";
import { Suspense, lazy, useEffect } from "react";
import { fetchUser } from "../store/slices/user-slice";
import { useAppDispatch, useAppSelector } from "../store/hooks";
import Loader from "./Loader/Loader";

const Home = lazy(() => import("../pages/Home/Home"));
const EventPage = lazy(() => import("../pages/Event/Event"));
const TicketPage = lazy(() => import("../pages/Ticket/Ticket"));
const AssignTicket = lazy(() => import("../pages/AssignTicket/AssignTicket"));
const Login = lazy(() => import("../pages/Login/Login"));
const Profile = lazy(() => import("../pages/Profile/Profile"));
const CartPage = lazy(() => import("../pages/Cart/Cart"));
const Verified = lazy(() => import("../pages/Verified/Verified"));
const LegalConditions = lazy(
  () => import("../pages/LegalConditions/LegalConditions")
);

const AppRoutes = () => {
  const token = localStorage.getItem("auth_token");
  const { loading } = useAppSelector((state) => state.user);
  const dispatch = useAppDispatch();

  useEffect(() => {
    if (token) {
      dispatch(fetchUser());
    }
  }, [dispatch]);

  if (loading) {
    return <Loader />;
  }

  return (
    <>
      <Suspense fallback={<Loader />}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path={RoutePaths.EventDetails} element={<EventPage />} />
          <Route path={RoutePaths.TicketDetails} element={<TicketPage />} />
          <Route path={RoutePaths.Assign} element={<AssignTicket />} />
          <Route path={RoutePaths.Cart} element={<CartPage />} />
          <Route path={RoutePaths.Login} element={<Login />} />
          <Route path={RoutePaths.Profile} element={<Profile />} />
          <Route path={RoutePaths.Verified} element={<Verified />} />
          <Route
            path={RoutePaths.PrivacyPolicy}
            element={<LegalConditions />}
          />
          <Route
            path={RoutePaths.TermsConditions}
            element={<LegalConditions />}
          />
          <Route path={RoutePaths.RefundPolicy} element={<LegalConditions />} />
          <Route
            path={RoutePaths.CancellationPolicy}
            element={<LegalConditions />}
          />
          <Route
            path={RoutePaths.ShippingDeliveryPolicy}
            element={<LegalConditions />}
          />
          <Route path={RoutePaths.ContactUs} element={<LegalConditions />} />
          <Route path="*" element={<Home />} />
        </Routes>
      </Suspense>
    </>
  );
};

export default AppRoutes;
