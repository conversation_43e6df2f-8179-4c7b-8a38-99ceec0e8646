import { useEffect, useRef } from "react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";

const EventMap = ({
  lat,
  lng,
  eventName,
}: {
  lat: number;
  lng: number;
  eventName: string;
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const leafletMapRef = useRef<L.Map | null>(null);

  useEffect(() => {
    if (mapRef.current && !leafletMapRef.current) {
      const map = L.map(mapRef.current).setView([lat, lng], 15);
      leafletMapRef.current = map;

      L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
        attribution: "&copy; OpenStreetMap contributors",
      }).addTo(map);

      const marker = L.marker([lat, lng]).addTo(map);
      marker.bindPopup(
        `<b>${eventName}</b><br><small>Click for directions</small>`
      );

      setTimeout(() => {
        marker.openPopup();
      }, 300);

      marker.on("add", () => {
        map.panBy([0, -80]);
      });

      marker.on("click", () => {
        window.open(
          `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`,
          "_blank"
        );
      });
    }

    return () => {
      leafletMapRef.current?.remove();
      leafletMapRef.current = null;
    };
  }, [lat, lng, eventName]);

  return (
    <div
      ref={mapRef}
      style={{
        width: "100%",
        height: "300px",
        borderRadius: "12px",
        overflow: "hidden",
      }}
    ></div>
  );
};

export default EventMap;
