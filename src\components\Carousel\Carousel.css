/* Preview Carousel Styles */
.hero-carousel-wrapper {
  position: relative;
  width: 100%;
  height: 450px;
  padding: 2rem 0;
  background: #f8f9fa;
  overflow: visible;
}

.preview-carousel {
  width: 100%;
  height: 100%;
  padding: 0 2rem;
}

.hero-slide {
  position: relative;
  width: 100%;
  height: 350px;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s ease;
  transform: scale(0.85);
  opacity: 0.7;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  cursor: pointer;
}

/* Active/Center slide styling */
.preview-carousel .swiper-slide-active {
  transform: scale(1) !important;
  opacity: 1 !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2) !important;
  z-index: 2 !important;
}

.hero-slide-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}

.hero-slide:hover .hero-slide-image {
  transform: scale(1.1);
}

.preview-carousel .swiper-slide-active:hover .hero-slide-image {
  transform: scale(1.05);
}

.hero-slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hero-slide:hover .hero-slide-overlay,
.preview-carousel .swiper-slide-active .hero-slide-overlay {
  opacity: 1;
}

.hero-slide-content {
  position: relative;
  z-index: 3;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  text-align: center;
}

.hero-content-container {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  min-height: 100vh;
}

.hero-content-main {
  max-width: 600px;
  color: white;
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-badge {
  display: inline-block;
  margin-bottom: 1.5rem;
  animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-badge-text {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-bottom: 2.5rem;
  animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-meta-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
  font-weight: 500;
}

.hero-meta-icon {
  width: 20px;
  height: 20px;
  color: #ff6b6b;
  flex-shrink: 0;
}

.hero-actions {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
  animation: fadeInUp 1s ease-out 0.8s both;
}

.hero-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 160px;
  justify-content: center;
}

.hero-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.hero-btn:hover::before {
  left: 100%;
}

.hero-btn-primary {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.hero-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.6);
}

.hero-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.hero-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.hero-btn-icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.hero-btn:hover .hero-btn-icon {
  transform: translateX(4px);
}

/* Navigation Controls */
.hero-carousel-navigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 1rem;
  z-index: 10;
  pointer-events: none;
}

.hero-nav-btn {
  pointer-events: all;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.hero-nav-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: scale(1.1);
}

.hero-nav-btn:active {
  transform: scale(0.95);
}

.hero-nav-btn svg {
  width: 24px;
  height: 24px;
}

/* Swiper Built-in Pagination Styling */
.preview-carousel .swiper-pagination {
  bottom: -3rem !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: auto !important;
  position: absolute !important;
  z-index: 10 !important;
}

.preview-carousel .swiper-pagination-bullet {
  width: 12px !important;
  height: 12px !important;
  background: rgba(0, 0, 0, 0.3) !important;
  opacity: 1 !important;
  margin: 0 6px !important;
  transition: all 0.3s ease !important;
  border-radius: 50% !important;
}

.preview-carousel .swiper-pagination-bullet:hover {
  background: rgba(0, 0, 0, 0.5) !important;
  transform: scale(1.2) !important;
}

.preview-carousel .swiper-pagination-bullet-active {
  background: #ff6b6b !important;
  transform: scale(1.3) !important;
}

.preview-carousel .swiper-pagination-bullet-active-main {
  background: #ff6b6b !important;
}

/* Progress Bar */
.hero-progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  z-index: 10;
}

.hero-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #ee5a24);
  width: 0%;
  transition: width 0.1s ease;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-carousel-wrapper {
    height: 80vh;
    min-height: 500px;
  }

  .hero-content-container {
    min-height: 80vh;
  }

  .hero-title {
    font-size: 3rem;
  }

  .hero-meta {
    gap: 1.5rem;
  }

  .hero-meta-item {
    font-size: 1rem;
  }

  .hero-actions {
    gap: 1rem;
  }

  .hero-btn {
    padding: 0.875rem 1.75rem;
    font-size: 1rem;
  }

  .hero-nav-btn {
    width: 50px;
    height: 50px;
  }

  .hero-nav-btn svg {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 768px) {
  .hero-carousel-wrapper {
    height: 350px;
    padding: 1rem 0;
  }

  .preview-carousel {
    padding: 0 1rem;
  }

  .hero-slide {
    height: 280px;
    border-radius: 15px;
  }

  .hero-slide-content {
    padding: 0.75rem;
  }

  .hero-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .hero-meta {
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .hero-meta-item {
    font-size: 0.95rem;
  }

  .hero-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .hero-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
    min-width: 140px;
  }

  .hero-carousel-navigation {
    padding: 0 1rem;
  }

  .hero-nav-btn {
    width: 45px;
    height: 45px;
  }

  .hero-nav-btn svg {
    width: 18px;
    height: 18px;
  }

  .preview-carousel .swiper-pagination-bullet {
    width: 10px !important;
    height: 10px !important;
    margin: 0 5px !important;
  }
}

@media (max-width: 480px) {
  .hero-carousel-wrapper {
    height: 300px;
    padding: 0.5rem 0;
  }

  .preview-carousel {
    padding: 0 0.5rem;
  }

  .hero-slide {
    height: 250px;
    border-radius: 12px;
  }

  .hero-slide-content {
    padding: 0.5rem;
  }

  .hero-title {
    font-size: 2rem;
    line-height: 1.2;
  }

  .hero-badge-text {
    font-size: 0.75rem;
    padding: 0.4rem 1.2rem;
  }

  .hero-meta-item {
    font-size: 0.875rem;
  }

  .hero-btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
    min-width: 120px;
  }

  .hero-carousel-navigation {
    display: none;
  }

  .preview-carousel .swiper-pagination {
    bottom: -2rem !important;
  }

  .preview-carousel .swiper-pagination-bullet {
    width: 8px !important;
    height: 8px !important;
    margin: 0 4px !important;
  }
}

/* High DPI Displays */
@media only screen and (-webkit-min-device-pixel-ratio: 2),
  only screen and (min-resolution: 192dpi) {
  .hero-slide-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .hero-slide-image,
  .hero-btn,
  .hero-nav-btn,
  .hero-pagination-bullet {
    transition: none;
  }

  .hero-content-main,
  .hero-badge,
  .hero-title,
  .hero-meta,
  .hero-actions {
    animation: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .hero-slide-overlay {
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.7) 0%,
      rgba(0, 0, 0, 0.4) 50%,
      rgba(0, 0, 0, 0.8) 100%
    );
  }
}
