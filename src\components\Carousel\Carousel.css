.carousel-wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 20px 0;
  background-color: var(--light-background-color);
  margin-bottom: 20px;
}

.carousel-container {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
  width: 100%;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding: 0 16px;
  gap: 16px;
  /* justify-content: center; */
}
.carousel-container::-webkit-scrollbar {
  display: none;
}

.carousel-slide {
  flex: 0 0 90%;
  max-width: 900px;
  scroll-snap-align: center;
  border-radius: 20px;
  overflow: hidden;
  transition: transform 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 100%;
  margin: 0 auto;
}

.carousel-slide.inactive {
  opacity: 0.5;
  transform: scale(0.95);
}
.carousel-slide.active {
  opacity: 1;
  transform: scale(1);
}

.carousel-slide img {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: cover;
}

.carousel-dots {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.carousel-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.dot {
  height: 8px;
  width: 8px;
  background-color: #ddd;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-top: 10px;
}

.dot.active,
.dot:hover {
  background-color: var(--secondary-color);
  transform: scale(1.2);
}

.info-section {
  width: 100%;
  padding: 15px;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row;
  gap: 12px;
  text-align: center;
  padding-left: 25px;
  line-height: 150%;
  margin-top: -5px;
}

.info-section button {
  background-color: #fff;
  color: var(--secondary-color);
  padding: 8px 6px 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  border: 1px solid var(--secondary-color);
  border-radius: 12px;
  font-size: 20px;
  font-weight: 500;
  line-height: 150%;
  white-space: nowrap;
}

.event-logo {
  width: 50px;
  height: 50px;
  object-fit: contain;
}

.info-event-name {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-color);
  text-align: left;
  line-height: 150%;
}

.info-event-date {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  gap: 10px;
}
.info-event-date p {
  color: var(--text-light);
}

.info-divider {
  width: 100%;
  height: 1px;
  background-color: #ddd;
  margin: 10px 0;
}

/* Mobile */
@media only screen and (max-width: 600px) {
  .carousel-wrapper {
    padding: 15px 0;
    margin-bottom: 15px;
  }

  .carousel-container {
    padding: 0 10px;
    gap: 12px;
  }

  .carousel-slide {
    flex: 0 0 100%;
  }

  .carousel-slide img {
    height: 200px;
  }

  .info-section {
    padding: 12px 15px;
    gap: 8px;
    flex-direction: column;
  }

  .info-event-name {
    font-size: 18px;
    font-weight: 600;
  }

  .info-section button {
    padding: 4px 10px;
    font-size: 14px;
    border-radius: 8px;
  }

  .dot {
    height: 6px;
    width: 6px;
  }
}

/* Tablets */
@media only screen and (min-width: 601px) and (max-width: 1024px) {
  .carousel-slide {
    flex: 0 0 90%;
  }

  .carousel-slide img {
    max-height: 300px;
  }

  .info-section {
    padding: 14px 20px;
    gap: 10px;
  }

  .info-event-name {
    font-size: 24px;
  }

  .info-section button {
    font-size: 16px;
    padding: 6px 12px;
  }
}

/* Laptops & Desktops */
@media only screen and (min-width: 1025px) {
  .carousel-slide {
    flex: 0 0 70%;
    max-width: 800px;
  }

  .carousel-slide img {
    max-height: 400px;
  }

  .carousel-container {
    padding: 0 40px;
    gap: 24px;
  }

  .info-event-name {
    font-size: 32px;
  }

  .info-section button {
    font-size: 18px;
    padding: 8px 16px;
  }

  .event-logo {
    width: 55px;
    height: 55px;
  }
}

/* Large Desktops */
@media only screen and (min-width: 1441px) {
  .carousel-slide {
    flex: 0 0 60%;
    max-width: 960px;
  }

  .carousel-slide img {
    max-height: 420px;
  }

  .info-event-name {
    font-size: 36px;
  }

  .info-section button {
    font-size: 20px;
  }

  .dot {
    height: 10px;
    width: 10px;
  }
}

@media only screen and (max-height: 600px) and (orientation: landscape) {
  .carousel-slide img {
    height: 180px;
  }

  .info-section {
    padding: 10px 15px;
  }

  .info-event-name {
    font-size: 16px;
  }

  .info-section button {
    padding: 5px 3px 5px 10px;
    font-size: 13px;
  }
}

@media only screen and (-webkit-min-device-pixel-ratio: 2),
  only screen and (min-resolution: 192dpi) {
  .carousel-slide img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
