.home-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  animation: fadeIn 1.5s ease-in;
}

.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: column;
  margin-top: 90px;
}

.card-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  padding: 0 30px;
}

.menu-section {
  display: flex;
  flex-direction: row;
  gap: 20px;
  align-items: center;
  width: 100%;
  margin: 30px 0;
}

.menu-item {
  font-size: 38px;
  text-transform: uppercase;
  font-weight: 700;
  white-space: nowrap;
  line-height: 150%;
  letter-spacing: 1.3px;
  color: var(--text-color);
}

.menu-item.selected {
  background-color: var(--primary-color);
  color: white;
  border-color: transparent;
}

.card-grid {
  display: flex;
  flex-direction: row;
  gap: 20px;
  width: 100%;
  overflow-y: visible;
  overflow-x: auto;
  padding-bottom: 50px;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.card-grid::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.card-wrapper {
  transition: transform 0.3s ease-in-out;
}

.card-container-even {
  background-color: var(--light-background-color);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .main-content {
    margin-top: 70px;
  }
}
