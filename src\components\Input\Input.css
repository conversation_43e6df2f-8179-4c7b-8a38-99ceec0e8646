.input-wrapper {
  margin-bottom: 1.2rem;
  display: flex;
  flex-direction: column;
}

.input-wrapper label {
  font-size: 0.95rem;
  margin-bottom: 0.4rem;
  color: #333;
}

.input-wrapper input {
  padding: 0.6rem 0.8rem;
  font-size: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  outline: none;
  transition: border-color 0.3s ease;
}

.input-wrapper input:focus {
  border-color: transparent;
}

.input-error {
  border-color: red;
}

.error-text {
  color: red;
  font-size: 0.8rem;
}

input:focus {
  outline: none;
  box-shadow: none;
  background-color: white; /* or any color */
}
input:-webkit-autofill {
  background-color: #f5f5f5 !important;
  box-shadow: 0 0 0px 1000px #f5f5f5 inset !important;
  -webkit-box-shadow: 0 0 0px 1000px #f5f5f5 inset !important;
  -webkit-text-fill-color: #000 !important;
}
