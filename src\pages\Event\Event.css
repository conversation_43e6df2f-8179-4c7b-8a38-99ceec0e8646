.event-container {
  text-align: center;
  margin: auto;
  margin-top: 97px;
  padding: 0 70px;
  padding-bottom: 70px;
}

.event-ticket-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.event-title {
  font-size: 32px;
  font-weight: bold;
  color: black;
  margin-bottom: 20px;
  text-transform: uppercase;
  text-align: left;
}

.left-section {
  text-align: left;
  width: 65%;
}

.event-image-container img {
  width: 100%;
  height: auto;
  border-radius: 10px;
  object-fit: cover;
}

.event-details {
  margin-top: 30px;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 10px;
}
.clamped-description {
  display: -webkit-box;
  -webkit-line-clamp: 3; /* show only 3 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.show-full {
  display: block;
}

.event-details .toggle-btn {
  cursor: pointer;
  font-weight: 700;
  color: var(--primary-color);
}
.event-details-container {
  display: none;
  align-items: flex-start;
  gap: 10px;
  flex-direction: column;
  margin-top: 15px;
}

.event-details-container div {
  display: flex;
  align-items: center;
  gap: 10px;
}

.artists-profile,
.sponsor-profile {
  display: flex;
  align-items: center;
  gap: 10px;
}

.artists-profile img {
  width: 100px;
  height: 150px;
  border-radius: 5px;
  object-fit: cover;
}

.sponsor-profile img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: fill;
}

.right-section {
  width: 30%;

  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 10px;

  position: sticky;
  top: 130px; /* adjust as needed to avoid header overlap */
  align-self: flex-start;
}

.event-details-section {
  text-align: left;
  display: flex;
  flex-direction: column;
  padding: 20px;
  gap: 10px;
  border-radius: 10px;
  border: 1px solid var(--border-color);
}

.event-map-container,
.left-event-map-container {
  width: 100%;
  height: 300px;
  border-radius: 10px;
  margin-top: 20px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  padding: 20px;
}

.left-event-map-container {
  display: none;
  padding: 0;
  height: 200px;
}
.divider {
  width: 100%;
  height: 1px;
  background-color: var(--border-color);
  margin: 10px 0;
}

.right-section .event-icon-container {
  display: flex;
  align-items: center;
  gap: 10px;
}
.ticket-btn-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.ticket-btn-section .price {
  font-weight: 700;
}

.ticket-btn-section .btn {
  width: 120px;
  padding: 10px;
  border-radius: 5px;
  white-space: nowrap;
  font-weight: 700;
  background-color: var(--primary-color);
  color: var(--btn-text-color);
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.btn a {
  text-decoration: none;
  color: var(--btn-text-color);
}

.event-footer {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 15px;
  font-size: 14px;
  animation: fadeIn 1.5s ease-in;
}

.event-footer p {
  margin: 0;
  padding: 0;
}

.bottom-booking-container {
  display: none;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-content {
  background: white;
  border-radius: 20px;
  animation: fadeIn 0.3s ease;
  position: relative;
  overflow: hidden;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .event-container {
    padding-left: 20px;
    padding-right: 20px;
  }
  .event-details-container {
    display: flex;
  }

  .left-section {
    width: 100%;
  }
  .right-section {
    display: none;
  }

  .left-event-map-container {
    display: block;
  }

  .footer {
    display: none;
  }

  .bottom-booking-container {
    display: block;
    background-color: pink;
    padding: 10px 20px;
    margin-top: -20px;
    position: fixed;
    width: 100%;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
  }

  .modal-content .login-wrapper .login-right {
    height: 270px !important;
  }
}

.modal-content .login-wrapper {
  height: 100% !important;
}
