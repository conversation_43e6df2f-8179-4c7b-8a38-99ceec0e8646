import { ITicketType, IVenueTicket } from "./event-ticket.interface";
import { IEvent } from "./organization.interface";

export interface User {
  id: string;
  firstName: string;
  lastName: string;
  phone: string;
  role: string;
  email?: string;
  gender?: "Male" | "Female" | "Other";
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  active?: boolean;
  OTP?: string;
  createdAt: Date;
  updatedAt: Date;
  profileImage: string;
  qrCode: string;
  emailVerified: boolean;
}

export interface IAssignTicket {
  _id: string;
  status: string;
  user: string;
  event: IEvent;
  venue: IVenueTicket;
  ticketType: string;
  quantity: number;
  price: number;
  createdAt: string;
  ticketTypeDetails: ITicketType;
}
