import { useState } from "react";
import "./Profile.css";
import Header from "../../components/Header/Header";
import { Icons } from "../../components/Icons/Icons";
import { useAppSelector } from "../../store/hooks";
import axiosInstance from "../../services/axiosInstance";

export default function Profile() {
  const { user } = useAppSelector((state) => state.user);
  const [editMode, setEditMode] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    profileImage: user?.profileImage || "",
    firstName: user?.firstName || "",
    lastName: user?.lastName || "",
    email: user?.email || "",
    phone: user?.phone || "",
    gender: user?.gender || "",
    address: user?.address || "",
    city: user?.city || "",
    state: user?.state || "",
    pincode: user?.pincode || "",
  });

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleUpdate = async () => {
    setIsLoading(true);
    try {
      await axiosInstance.put("/users/profile", formData);
      console.log("Update success:", formData);
      setEditMode(false);
    } catch (err) {
      console.error("Update failed:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      profileImage: user?.profileImage || "",
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      email: user?.email || "",
      phone: user?.phone || "",
      gender: user?.gender || "",
      address: user?.address || "",
      city: user?.city || "",
      state: user?.state || "",
      pincode: user?.pincode || "",
    });
    setEditMode(false);
  };

  return (
    <>
      <Header />
      <div className="profile-container">
        <div className="profile-wrapper">
          <div className="profile-card">
            <div className="profile-card-header">
              <div className="profile-info-text">
                <h2>Personal Information</h2>
                <p>Keep your details up to date</p>
              </div>
              {!editMode ? (
                <button onClick={() => setEditMode(true)} className="edit-btn">
                  <Icons.ProfileEdit /> Edit Profile
                </button>
              ) : (
                <div className="action-buttons">
                  <button
                    onClick={handleUpdate}
                    disabled={isLoading}
                    className="save-btn"
                  >
                    ✔ {isLoading ? "Saving..." : "Save"}
                  </button>
                  <button onClick={handleCancel} className="cancel-btn">
                    ✖ Cancel
                  </button>
                </div>
              )}
            </div>

            <div className="profile-card-content">
              <div className="profile-avatar">
                <div className="avatar-img">
                  {formData?.profileImage ? (
                    <img src={formData.profileImage} />
                  ) : (
                    <div className="avatar-initials">
                      {formData?.firstName &&
                        formData?.firstName
                          ?.split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase() +
                          " " +
                          formData?.lastName
                            ?.split(" ")
                            .map((n) => n[0])
                            .join("")
                            .toUpperCase()}
                    </div>
                  )}
                </div>
              </div>

              <div className="profile-basic-info">
                {editMode ? (
                  <>
                    <input
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleChange}
                      placeholder="First Name"
                    />
                    <input
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleChange}
                      placeholder="Last Name"
                    />
                    <input
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="Email Address"
                      type="email"
                    />
                  </>
                ) : (
                  <>
                    {formData.firstName && (
                      <h3>{formData.firstName + " " + formData.lastName}</h3>
                    )}
                    {formData.email && (
                      <p className="profile-email-info">
                        <Icons.Email /> {formData.email}
                      </p>
                    )}
                    <span className="active-status">Active Account</span>
                  </>
                )}
              </div>

              <div className="form-grid">
                <div className="form-group">
                  <label>
                    <Icons.Phone stroke="rgb(37 99 235 )" />
                    Phone Number
                  </label>
                  <input
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    disabled={!editMode}
                  />
                </div>
                <div className="form-group">
                  <label>
                    <Icons.Gender stroke="rgb(147 51 234)" />
                    Gender
                  </label>
                  <select
                    name="gender"
                    value={formData.gender}
                    onChange={handleChange}
                    disabled={!editMode}
                  >
                    <option value="">Select Gender</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                <div className="form-group full-width">
                  <label>
                    <Icons.Address stroke="rgb(220 38 38)" />
                    Address
                  </label>
                  <input
                    name="address"
                    value={formData.address}
                    onChange={handleChange}
                    disabled={!editMode}
                  />
                </div>
                <div className="form-group">
                  <label>
                    <Icons.City stroke="rgb(22 163 74)" />
                    City
                  </label>
                  <input
                    name="city"
                    value={formData.city}
                    onChange={handleChange}
                    disabled={!editMode}
                  />
                </div>
                <div className="form-group">
                  <label>
                    <Icons.State stroke="rgb(234 88 12)" />
                    State
                  </label>
                  <input
                    name="state"
                    value={formData.state}
                    onChange={handleChange}
                    disabled={!editMode}
                  />
                </div>
                <div className="form-group">
                  <label>
                    <Icons.PinCode stroke="rgb(79 70 229 )" />
                    Pincode
                  </label>
                  <input
                    name="pincode"
                    value={formData.pincode}
                    onChange={handleChange}
                    disabled={!editMode}
                  />
                </div>
              </div>

              {!editMode && (
                <div className="profile-status">
                  <p className="status-header">
                    <Icons.User />
                    Account Status:
                  </p>
                  <p className="status-text">
                    Your profile is complete and verified. Last updated on{" "}
                    {new Date().toLocaleDateString()}.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
