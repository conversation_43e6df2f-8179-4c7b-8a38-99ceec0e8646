import React from "react";

export const Icons = {
  Calendar: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      viewBox="0 0 24 24"
      {...props}
    >
      <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
      <line x1="16" y1="2" x2="16" y2="6" />
      <line x1="8" y1="2" x2="8" y2="6" />
      <line x1="3" y1="10" x2="21" y2="10" />
    </svg>
  ),

  Clock: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      viewBox="0 0 24 24"
      {...props}
    >
      <circle cx="12" cy="12" r="10" />
      <polyline points="12 6 12 12 16 14" />
    </svg>
  ),
  SandClock: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      fill="#000000"
      height="24px"
      width="24px"
      version="1.1"
      id="Capa_1"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 30.393 30.393"
      {...props}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        {" "}
        <g>
          {" "}
          <path d="M20.842,6.723V5.775H9.553v0.947c0,0,1.717,4.781,4.939,6.988v2.297c0,0-3.554,3.459-4.939,7.434v1.084h11.288v-1.084 c-1.801-4.389-4.939-7.434-4.939-7.434V13.71C20.284,9.945,20.842,6.723,20.842,6.723z"></path>{" "}
          <path d="M24.91,4.963V3.256h1.619V0H3.864v3.256h1.621v1.707c0,2.607,4.677,9.725,4.896,10.238 c-0.215,0.514-4.896,7.633-4.896,10.229v1.705H3.864v3.258h22.665v-3.258H24.91V25.43c0-2.607-4.679-9.68-4.929-10.234 C20.231,14.643,24.91,7.568,24.91,4.963z M23.293,25.43v1.705H7.103V25.43c0-1.873,4.96-9.295,4.96-10.234s-4.96-8.359-4.96-10.232 V3.256h16.189v1.707c0,1.873-5.002,9.293-5.002,10.232S23.293,23.557,23.293,25.43z"></path>{" "}
          <g> </g> <g> </g> <g> </g> <g> </g> <g> </g> <g> </g> <g> </g>{" "}
          <g> </g> <g> </g> <g> </g> <g> </g> <g> </g> <g> </g> <g> </g>{" "}
          <g> </g>{" "}
        </g>{" "}
      </g>
    </svg>
  ),

  Attendees: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      width="24px"
      height="24px"
      viewBox="0 0 24.00 24.00"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      stroke="#000000"
      strokeWidth="0.84000000000000005"
      {...props}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        {" "}
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3 18C3 15.3945 4.66081 13.1768 6.98156 12.348C7.61232 12.1227 8.29183 12 9 12C9.70817 12 10.3877 12.1227 11.0184 12.348C11.3611 12.4703 11.6893 12.623 12 12.8027C12.3107 12.623 12.6389 12.4703 12.9816 12.348C13.6123 12.1227 14.2918 12 15 12C15.7082 12 16.3877 12.1227 17.0184 12.348C19.3392 13.1768 21 15.3945 21 18V21H15.75V19.5H19.5V18C19.5 15.5147 17.4853 13.5 15 13.5C14.4029 13.5 13.833 13.6163 13.3116 13.8275C14.3568 14.9073 15 16.3785 15 18V21H3V18ZM9 11.25C8.31104 11.25 7.66548 11.0642 7.11068 10.74C5.9977 10.0896 5.25 8.88211 5.25 7.5C5.25 5.42893 6.92893 3.75 9 3.75C10.2267 3.75 11.3158 4.33901 12 5.24963C12.6842 4.33901 13.7733 3.75 15 3.75C17.0711 3.75 18.75 5.42893 18.75 7.5C18.75 8.88211 18.0023 10.0896 16.8893 10.74C16.3345 11.0642 15.689 11.25 15 11.25C14.311 11.25 13.6655 11.0642 13.1107 10.74C12.6776 10.4869 12.2999 10.1495 12 9.75036C11.7001 10.1496 11.3224 10.4869 10.8893 10.74C10.3345 11.0642 9.68896 11.25 9 11.25ZM13.5 18V19.5H4.5V18C4.5 15.5147 6.51472 13.5 9 13.5C11.4853 13.5 13.5 15.5147 13.5 18ZM11.25 7.5C11.25 8.74264 10.2426 9.75 9 9.75C7.75736 9.75 6.75 8.74264 6.75 7.5C6.75 6.25736 7.75736 5.25 9 5.25C10.2426 5.25 11.25 6.25736 11.25 7.5ZM15 5.25C13.7574 5.25 12.75 6.25736 12.75 7.5C12.75 8.74264 13.7574 9.75 15 9.75C16.2426 9.75 17.25 8.74264 17.25 7.5C17.25 6.25736 16.2426 5.25 15 5.25Z"
          fill="#000000"
        ></path>{" "}
      </g>
    </svg>
  ),

  Location: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      viewBox="0 0 24 24"
      {...props}
    >
      <path d="M21 10c0 6-9 13-9 13S3 16 3 10a9 9 0 1 1 18 0z" />
      <circle cx="12" cy="10" r="3" />
    </svg>
  ),

  ProfileEdit: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      className="w-4 h-4"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
      />
    </svg>
  ),

  Email: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      className="w-4 h-4"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
      />
    </svg>
  ),

  Phone: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      className="w-4 h-4 text-blue-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
      />
    </svg>
  ),

  Gender: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      className="w-4 h-4 text-purple-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
      />
    </svg>
  ),

  Address: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      className="w-4 h-4 text-red-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
      />
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
      />
    </svg>
  ),

  City: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      className="w-4 h-4 text-green-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
      />
    </svg>
  ),

  State: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      className="w-4 h-4 text-orange-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
      />
    </svg>
  ),

  PinCode: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      className="w-4 h-4 text-indigo-600"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"
      />
    </svg>
  ),

  User: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      className="w-5 h-5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
      />
    </svg>
  ),

  LeftChevron: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M10.4913 7.80952C10.0363 8.26452 10.0363 8.99952 10.4913 9.45452L15.0179 13.9812L10.4913 18.5079C10.0363 18.9629 10.0363 19.6979 10.4913 20.1529C10.9463 20.6079 11.6813 20.6079 12.1363 20.1529L17.4913 14.7979C17.9463 14.3429 17.9463 13.6079 17.4913 13.1529L12.1363 7.79785C11.6929 7.35452 10.9463 7.35452 10.4913 7.80952Z"
        fill={props.fill || "white"}
      />
    </svg>
  ),
  DownChevron: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17.3063 8.9925C16.9163 8.6025 16.2863 8.6025 15.8963 8.9925L12.0163 12.8725L8.13634 8.9925C7.74634 8.6025 7.11633 8.6025 6.72633 8.9925C6.33633 9.3825 6.33633 10.0125 6.72633 10.4025L11.3163 14.9925C11.7063 15.3825 12.3363 15.3825 12.7263 14.9925L17.3163 10.4025C17.6963 10.0225 17.6963 9.3825 17.3063 8.9925Z"
        fill="#E63A4A"
      />
    </svg>
  ),
  Cart: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g opacity="0.5">
        <path
          d="M17.4394 4.49792C17.2441 4.2635 16.9995 4.07497 16.7231 3.94571C16.4467 3.81645 16.1452 3.74963 15.8401 3.75001H4.61234L4.58317 3.50626C4.52348 2.9996 4.27997 2.53246 3.89879 2.19339C3.51761 1.85433 3.02527 1.66691 2.51511 1.66667H2.36095C2.17677 1.66667 2.00014 1.73984 1.8699 1.87007C1.73967 2.0003 1.6665 2.17694 1.6665 2.36112C1.6665 2.54529 1.73967 2.72193 1.8699 2.85216C2.00014 2.9824 2.17677 3.05556 2.36095 3.05556H2.51511C2.68521 3.05558 2.84938 3.11803 2.97648 3.23106C3.10359 3.34408 3.1848 3.49983 3.2047 3.66876L4.16025 11.7938C4.25946 12.6387 4.66545 13.4179 5.30116 13.9833C5.93688 14.5488 6.75807 14.8611 7.60886 14.8611H14.8609C15.0451 14.8611 15.2218 14.788 15.352 14.6577C15.4822 14.5275 15.5554 14.3509 15.5554 14.1667C15.5554 13.9825 15.4822 13.8059 15.352 13.6756C15.2218 13.5454 15.0451 13.4722 14.8609 13.4722H7.60886C7.17904 13.471 6.76011 13.3369 6.40951 13.0882C6.05891 12.8396 5.7938 12.4886 5.65053 12.0833H13.9283C14.7424 12.0834 15.5306 11.7974 16.1553 11.2753C16.7799 10.7532 17.2012 10.0283 17.3457 9.22709L17.8908 6.20348C17.9452 5.90348 17.933 5.59519 17.855 5.30045C17.777 5.00571 17.6351 4.73173 17.4394 4.49792ZM16.5276 5.95695L15.9818 8.98056C15.8951 9.46181 15.6418 9.89722 15.2664 10.2106C14.891 10.5239 14.4173 10.6952 13.9283 10.6944H5.4297L4.77623 5.13889H15.8401C15.9421 5.13828 16.043 5.16016 16.1356 5.20296C16.2282 5.24576 16.3103 5.30844 16.3759 5.38654C16.4415 5.46464 16.4892 5.55624 16.5154 5.65482C16.5416 5.75341 16.5458 5.85656 16.5276 5.95695Z"
          fill="#E63A4A"
        />
        <path
          d="M6.52761 18.3333C7.29468 18.3333 7.9165 17.7115 7.9165 16.9444C7.9165 16.1774 7.29468 15.5556 6.52761 15.5556C5.76055 15.5556 5.13873 16.1774 5.13873 16.9444C5.13873 17.7115 5.76055 18.3333 6.52761 18.3333Z"
          fill="#E63A4A"
        />
        <path
          d="M13.4721 18.3333C14.2391 18.3333 14.8609 17.7115 14.8609 16.9444C14.8609 16.1774 14.2391 15.5556 13.4721 15.5556C12.705 15.5556 12.0832 16.1774 12.0832 16.9444C12.0832 17.7115 12.705 18.3333 13.4721 18.3333Z"
          fill="#E63A4A"
        />
      </g>
    </svg>
  ),
  Ticket: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g opacity="0.5">
        <path
          d="M12.7778 1.66666H12.6875C12.3767 1.66962 12.0757 1.7757 11.8317 1.96826C11.5878 2.16082 11.4147 2.42895 11.3396 2.73055C11.2592 3.02419 11.0845 3.28328 10.8425 3.46795C10.6005 3.65263 10.3044 3.75266 10 3.75266C9.69555 3.75266 9.39954 3.65263 9.15751 3.46795C8.91547 3.28328 8.74082 3.02419 8.66042 2.73055C8.58535 2.42895 8.41224 2.16082 8.16827 1.96826C7.9243 1.7757 7.62329 1.66962 7.3125 1.66666H7.22222C6.30167 1.66777 5.41914 2.03394 4.76821 2.68487C4.11728 3.3358 3.7511 4.21833 3.75 5.13889V16.25C3.75 16.8025 3.96949 17.3324 4.36019 17.7231C4.7509 18.1138 5.2808 18.3333 5.83333 18.3333H7.3125C7.62329 18.3304 7.9243 18.2243 8.16827 18.0317C8.41224 17.8392 8.58535 17.571 8.66042 17.2694C8.74082 16.9758 8.91547 16.7167 9.15751 16.532C9.39954 16.3474 9.69555 16.2473 10 16.2473C10.3044 16.2473 10.6005 16.3474 10.8425 16.532C11.0845 16.7167 11.2592 16.9758 11.3396 17.2694C11.4147 17.571 11.5878 17.8392 11.8317 18.0317C12.0757 18.2243 12.3767 18.3304 12.6875 18.3333H14.1667C14.7192 18.3333 15.2491 18.1138 15.6398 17.7231C16.0305 17.3324 16.25 16.8025 16.25 16.25V5.13889C16.2489 4.21833 15.8827 3.3358 15.2318 2.68487C14.5809 2.03394 13.6983 1.66777 12.7778 1.66666ZM14.1667 16.9444L12.6785 16.9007C12.5154 16.3114 12.1624 15.7924 11.6742 15.4241C11.1861 15.0559 10.5901 14.859 9.9787 14.864C9.36727 14.869 8.77457 15.0756 8.29251 15.4517C7.81046 15.8279 7.46598 16.3526 7.3125 16.9444H5.83333C5.64916 16.9444 5.47252 16.8713 5.34229 16.741C5.21205 16.6108 5.13889 16.4342 5.13889 16.25V13.4722H6.52778C6.71196 13.4722 6.88859 13.3991 7.01882 13.2688C7.14906 13.1386 7.22222 12.962 7.22222 12.7778C7.22222 12.5936 7.14906 12.417 7.01882 12.2867C6.88859 12.1565 6.71196 12.0833 6.52778 12.0833H5.13889V5.13889C5.13889 4.58635 5.35838 4.05645 5.74908 3.66575C6.13978 3.27505 6.66969 3.05555 7.22222 3.05555L7.32153 3.0993C7.48418 3.68502 7.83402 4.20142 8.31765 4.56969C8.80128 4.93795 9.39212 5.13786 10 5.13889C10.6161 5.13364 11.2136 4.92729 11.7016 4.55122C12.1896 4.17515 12.5414 3.64996 12.7035 3.05555H12.7778C13.3303 3.05555 13.8602 3.27505 14.2509 3.66575C14.6416 4.05645 14.8611 4.58635 14.8611 5.13889V12.0833H13.4722C13.288 12.0833 13.1114 12.1565 12.9812 12.2867C12.8509 12.417 12.7778 12.5936 12.7778 12.7778C12.7778 12.962 12.8509 13.1386 12.9812 13.2688C13.1114 13.3991 13.288 13.4722 13.4722 13.4722H14.8611V16.25C14.8611 16.4342 14.7879 16.6108 14.6577 16.741C14.5275 16.8713 14.3508 16.9444 14.1667 16.9444Z"
          fill="#E63A4A"
        />
        <path
          d="M10.6944 12.0833H9.30556C9.12138 12.0833 8.94474 12.1565 8.81451 12.2867C8.68428 12.417 8.61111 12.5936 8.61111 12.7778C8.61111 12.962 8.68428 13.1386 8.81451 13.2688C8.94474 13.3991 9.12138 13.4722 9.30556 13.4722H10.6944C10.8786 13.4722 11.0553 13.3991 11.1855 13.2688C11.3157 13.1386 11.3889 12.962 11.3889 12.7778C11.3889 12.5936 11.3157 12.417 11.1855 12.2867C11.0553 12.1565 10.8786 12.0833 10.6944 12.0833Z"
          fill="#E63A4A"
        />
      </g>
    </svg>
  ),
  UserProfile: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g opacity="0.5">
        <path
          d="M10.1389 2.50002C4.78472 2.50002 2.5 4.78474 2.5 10.1389C2.5 15.4931 4.78472 17.7778 10.1389 17.7778C15.4931 17.7778 17.7778 15.4931 17.7778 10.1389C17.7778 4.78474 15.4931 2.50002 10.1389 2.50002ZM7.15764 16.0243C7.41944 14.9521 8.10903 14.3056 10.1389 14.3056C12.1688 14.3056 12.859 14.9521 13.1201 16.0243C12.3035 16.2722 11.3188 16.3889 10.1389 16.3889C8.95903 16.3889 7.97431 16.2722 7.15764 16.0243ZM14.3889 15.4375C13.6861 13.2035 11.6694 12.916 10.1396 12.916C8.60972 12.916 6.59306 13.2028 5.89028 15.4375C4.48194 14.5 3.88958 12.8167 3.88958 10.1382C3.88889 5.58265 5.58264 3.8889 10.1389 3.8889C14.6951 3.8889 16.3889 5.58265 16.3889 10.1389C16.3889 12.8174 15.7965 14.5007 14.3882 15.4382L14.3889 15.4375ZM10.1389 5.97224C8.27014 5.97224 7.36111 6.88127 7.36111 8.75002C7.36111 10.6188 8.27014 11.5278 10.1389 11.5278C12.0076 11.5278 12.9167 10.6188 12.9167 8.75002C12.9167 6.88127 12.0076 5.97224 10.1389 5.97224ZM10.1389 10.1389C9.03542 10.1389 8.75 9.85349 8.75 8.75002C8.75 7.64654 9.03542 7.36113 10.1389 7.36113C11.2424 7.36113 11.5278 7.64654 11.5278 8.75002C11.5278 9.85349 11.2424 10.1389 10.1389 10.1389Z"
          fill="#E63A4A"
        />
      </g>
    </svg>
  ),
  LogOut: (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g opacity="0.5">
        <path
          d="M9.63595 12.0833C9.45177 12.0833 9.27514 12.1565 9.1449 12.2867C9.01467 12.417 8.9415 12.5936 8.9415 12.7778V14.8611C8.9415 15.4136 8.72201 15.9436 8.33131 16.3343C7.94061 16.725 7.4107 16.9444 6.85817 16.9444H5.13873C4.58619 16.9444 4.05629 16.725 3.66559 16.3343C3.27489 15.9436 3.05539 15.4136 3.05539 14.8611V5.13889C3.05539 4.58636 3.27489 4.05646 3.66559 3.66575C4.05629 3.27505 4.58619 3.05556 5.13873 3.05556H6.85817C7.4107 3.05556 7.94061 3.27505 8.33131 3.66575C8.72201 4.05646 8.9415 4.58636 8.9415 5.13889V7.22223C8.9415 7.4064 9.01467 7.58304 9.1449 7.71327C9.27514 7.84351 9.45177 7.91667 9.63595 7.91667C9.82013 7.91667 9.99676 7.84351 10.127 7.71327C10.2572 7.58304 10.3304 7.4064 10.3304 7.22223V5.13889C10.3293 4.21834 9.96311 3.33581 9.31218 2.68488C8.66125 2.03395 7.77872 1.66777 6.85817 1.66667H5.13873C4.21817 1.66777 3.33564 2.03395 2.68471 2.68488C2.03378 3.33581 1.66761 4.21834 1.6665 5.13889V14.8611C1.66761 15.7817 2.03378 16.6642 2.68471 17.3151C3.33564 17.9661 4.21817 18.3322 5.13873 18.3333H6.85817C7.77872 18.3322 8.66125 17.9661 9.31218 17.3151C9.96311 16.6642 10.3293 15.7817 10.3304 14.8611V12.7778C10.3304 12.5936 10.2572 12.417 10.127 12.2867C9.99676 12.1565 9.82013 12.0833 9.63595 12.0833Z"
          fill="#E63A4A"
        />
        <path
          d="M17.5464 8.52709L14.3616 5.34237C14.2976 5.27604 14.221 5.22314 14.1362 5.18674C14.0515 5.15034 13.9604 5.13119 13.8682 5.13039C13.776 5.12959 13.6845 5.14716 13.5992 5.18207C13.5138 5.21699 13.4363 5.26856 13.3711 5.33376C13.3059 5.39896 13.2543 5.4765 13.2194 5.56184C13.1845 5.64719 13.1669 5.73863 13.1677 5.83084C13.1685 5.92305 13.1877 6.01417 13.2241 6.0989C13.2605 6.18362 13.3134 6.26025 13.3797 6.32431L16.3394 9.28473L5.83317 9.30556C5.64899 9.30556 5.47236 9.37873 5.34212 9.50896C5.21189 9.63919 5.13873 9.81583 5.13873 10C5.13873 10.1842 5.21189 10.3608 5.34212 10.4911C5.47236 10.6213 5.64899 10.6944 5.83317 10.6944L16.3804 10.6729L13.3783 13.6757C13.312 13.7398 13.2591 13.8164 13.2227 13.9011C13.1863 13.9858 13.1671 14.077 13.1663 14.1692C13.1655 14.2614 13.1831 14.3528 13.218 14.4382C13.2529 14.5235 13.3045 14.601 13.3697 14.6663C13.4349 14.7315 13.5124 14.783 13.5978 14.8179C13.6831 14.8529 13.7746 14.8704 13.8668 14.8696C13.959 14.8688 14.0501 14.8497 14.1348 14.8133C14.2196 14.7769 14.2962 14.724 14.3603 14.6576L17.545 11.4729C17.9357 11.0824 18.1554 10.5527 18.1556 10.0003C18.1559 9.44787 17.9367 8.91795 17.5464 8.52709Z"
          fill="#E63A4A"
        />
      </g>
    </svg>
  ),

  // Add more icons as needed
};
